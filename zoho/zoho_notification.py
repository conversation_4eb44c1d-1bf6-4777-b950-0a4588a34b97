import os
from typing import Any, Dict

import httpx
from dotenv import load_dotenv
from loguru import logger


async def send_notification_to_zoho(notification: dict) -> Dict[str, Any]:
    """
    发送通知到 Zoho Flow

    Args:
        notification: 通知数据字典， 格式如下：
        {
            "message": "This is a test message, please ignore.", # 通知内容
            "email": "<EMAIL>", # 接收通知的电子邮件地址
            "title": "AI Agent",
        }

    Returns:
        Dict[str, Any]: 包含发送结果的字典，格式如下：
        {
            "success": bool, # 是否成功发送
            "status_code": int, # HTTP状态码，失败时为0
            "message": str, # 响应消息或错误信息
            "response_data": dict | None, # 解析后的响应数据（如果是JSON）
            "error": str | None # 错误详情（如果有错误）
        }
    """
    result = {"success": False, "status_code": 0, "message": "", "response_data": None, "error": None}

    try:
        # 检查必要的环境变量
        api_key = os.environ.get("ZOHO_NOTIFICATION_API_KEY")
        if not api_key:
            result["error"] = "ZOHO_NOTIFICATION_API_KEY environment variable not found"
            logger.error(f"Zoho notification skipped: {result['error']}")
            return result

        # 发送请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"https://flow.zoho.com/770204158/flow/webhook/incoming?zapikey={api_key}&isdebug=false",
                json=notification,
            )

            result["status_code"] = response.status_code
            result["message"] = response.text or ""

            # 检查响应状态
            if response.status_code in (200, 201, 202):
                result["success"] = True
                logger.info(
                    f"Zoho notification sent successfully - Status: {response.status_code}, Response: {response.text}"
                )
            else:
                result["error"] = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"Zoho notification {notification} failed - {result['error']}")

            # 尝试解析JSON响应
            try:
                if response.text:
                    result["response_data"] = response.json()
            except Exception:
                # 响应不是有效的JSON，这是正常的，不记录为错误
                pass

    except httpx.TimeoutException as e:
        result["error"] = f"Request timeout: {str(e)}"
        logger.error(f"Zoho notification timeout: {result['error']}")
    except httpx.RequestError as e:
        result["error"] = f"Request error: {str(e)}"
        logger.error(f"Zoho notification request error: {result['error']}")
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
        logger.error(f"Zoho notification unexpected error: {result['error']}")

    return result


if __name__ == "__main__":
    import asyncio

    load_dotenv()

    notification = {
        "message": "Your lookalike companies task is complete. 5 companies have been identified and are viewable under the 'Lookalike Companies' section within the ABC details page.",  # noqa: E501
        "email": "<EMAIL>",
        "title": "AI Agent",
    }
    asyncio.run(send_notification_to_zoho(notification))
