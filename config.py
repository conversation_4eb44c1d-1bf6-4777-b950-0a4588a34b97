import os
from typing import Callable, Type, TypeVar

T = TypeVar("T")


# Helper function to get environment variables with defaults and type casting
def get_env_var(name: str, default: T, type_cast: Callable[[str], T] | Type[T] | None = None) -> T:
    value_str = os.environ.get(name)
    if value_str is None:
        return default

    caster = type_cast if type_cast is not None else type(default)

    # Handle boolean parsing specifically if default is bool
    if isinstance(default, bool):
        if value_str.lower() in ("true", "1", "yes", "y"):
            return True  # type: ignore
        elif value_str.lower() in ("false", "0", "no", "n"):
            return False  # type: ignore
        else:
            print(f"Warning: Invalid boolean value for {name}: '{value_str}'. Using default: {default}")
            return default

    try:
        # Attempt to cast using the inferred or provided type caster
        if caster is None:  # Should not happen due to logic above, but for type safety
            return default
        return caster(value_str)  # type: ignore
    except (ValueError, TypeError):
        print(f"Warning: Invalid value for {name}: '{value_str}'. Using default: {default}")
        return default


# OpenAI 配置
OPENAI_API_KEY = get_env_var(
    "OPENAI_API_KEY",
    "********************************************************************************************************************************************************************",
)
MODEL = get_env_var("MODEL", "gpt-4.1")
CONTACTS_AGENT_MODEL = get_env_var("CONTACTS_AGENT_MODEL", "gpt-5")
CONTACTS_RECURSION_LIMIT = get_env_var("CONTACTS_RECURSION_LIMIT", 100, int)

# Tavily API配置
TAVILY_API_KEY = get_env_var("TAVILY_API_KEY", "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")

# 代理配置
HTTPS_PROXY = get_env_var("HTTPS_PROXY", None)

# Rapid API 配置
RAPID_ENABLE = get_env_var("RAPID_ENABLE", "false").lower() == "true"
RAPID_API_URL = get_env_var("RAPID_API_URL", "https://linkedin-api8.p.rapidapi.com/data-connection-count")
RAPID_API_KEY = get_env_var("RAPID_API_KEY", "97aa10731emsh3182b3a75132a8ep168f79jsne88439b3a56a")
RAPID_API_HOST = get_env_var("RAPID_API_HOST", "linkedin-api8.p.rapidapi.com")

# 环境配置
ENVIRONMENT = get_env_var("ENVIRONMENT", "local")

# Langfuse 配置
LANGFUSE_SECRET_KEY = get_env_var("LANGFUSE_SECRET_KEY", "******************************************")
LANGFUSE_PUBLIC_KEY = get_env_var("LANGFUSE_PUBLIC_KEY", "pk-lf-f01f59e9-a2aa-4a74-9f50-94280a3afaba")
LANGFUSE_HOST = get_env_var("LANGFUSE_HOST", "https://langfuse.inhand.online")

# 服务器配置
SERVER_HOST = get_env_var("SERVER_HOST", "0.0.0.0")
SERVER_PORT = get_env_var("SERVER_PORT", 3000, int)
SERVER_DOMAIN = get_env_var("SERVER_DOMAIN", "https://poweris.inhandnetworks.com")

# SSE配置
SSE_TIMEOUT = get_env_var("SSE_TIMEOUT", 120, int)

# 任务池配置
MAX_CONCURRENT_TASKS = get_env_var("MAX_CONCURRENT_TASKS", 1, int)
TASK_CLEANUP_INTERVAL = get_env_var("TASK_CLEANUP_INTERVAL", 3600, int)

# Taskiq 配置
TASK_REDIS_URL = get_env_var("TASK_REDIS_URL", "redis://localhost:6379")
TASK_REDIS_QUEUE_NAME = get_env_var("TASK_REDIS_QUEUE_NAME", "sales_agent_detect_customer_queue")
TASK_REDIS_QUEUE_CONSUMER_GROUP = get_env_var("TASK_REDIS_CONSUMER_GROUP", "sales_agent_detect_customer_group")
TASK_REDIS_QUEUE_CONSUMER_NAME = get_env_var("TASK_REDIS_CONSUMER_NAME", "sales_agent_detect_customer_consumer")
TASK_REDIS_BACKEND_PREFIX = get_env_var("TASK_REDIS_BACKEND_PREFIX", "sales_agent_detect_customer_backend")
TASK_RESULT_EXPIRE_TIME = get_env_var("TASK_RESULT_EXPIRE_TIME", 3600 * 24 * 30)

# 任务超时配置
TASK_TIMEOUT_MINUTES = get_env_var("TASK_TIMEOUT_MINUTES", 60 * 2, int)
TASK_CLEANUP_INTERVAL_MINUTES = get_env_var("TASK_CLEANUP_INTERVAL_MINUTES", 10, int)

# MongoDB配置
MONGODB_URI = get_env_var("MONGODB_URI", "mongodb://localhost:27017/")
MONGODB_DB_NAME = get_env_var("MONGODB_DB_NAME", "sales_agent")
MONGODB_COLLECTION_NAME = get_env_var("MONGODB_COLLECTION_NAME", "task_status")
MONGODB_RESULTS_COLLECTION_NAME = MONGODB_COLLECTION_NAME

# 日志配置
LOG_LEVEL = get_env_var("LOG_LEVEL", "INFO")

# Apollo API配置
APOLLO_API_KEY = get_env_var("APOLLO_API_KEY", "HDGcKVV03dUNZ9-8QtLIyg")
APOLLO_BASE_URL = get_env_var("APOLLO_BASE_URL", "https://api.apollo.io/api/v1")

# Zoho API配置
ZOHO_API_KEY = get_env_var("ZOHO_API_KEY", "0ecf03710f684591a9aed0d9a16401de")
ZOHO_API_HOST = get_env_var("ZOHO_API_HOST", "https://poweris.inhandnetworks.com")

# Http Client配置
MAX_KEEPALIVE_CONNECTIONS = get_env_var("MAX_KEEPALIVE_CONNECTIONS", 10, int)
