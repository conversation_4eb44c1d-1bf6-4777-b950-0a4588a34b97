"""
流媒体消息相关类型和模型定义模块
"""

import asyncio
import inspect
import json
from collections.abc import AsyncIterable
from contextlib import contextmanager
from contextvars import <PERSON>textV<PERSON>
from datetime import datetime, timezone
from enum import Enum
from typing import Any, AsyncGenerator, Awaitable, Callable, Coroutine, Generator, Optional, Union, cast
from uuid import UUID

from langchain_core.callbacks import Async<PERSON>allbackHandler
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.outputs import LLMResult
from langchain_core.tracers.context import register_configure_hook
from loguru import logger
from pydantic import BaseModel, Field
from taskiq import TaskiqResult


class StreamMessageType(str, Enum):
    """Stream message type enum"""

    THINKING = "thinking"  # Reasoning chunk
    TEXT = "text"  # Output text
    DATA = "data"  # Data object

    TOOL_CALL = "tool_call"  # Tool call
    TOOL_RESULT = "tool_result"  # Tool result

    STEP_START = "step_start"  # Step start
    STEP_END = "step_end"  # Step end

    FINISH = "finish"  # Finish
    ERROR = "error"  # Error


class StreamMessage(BaseModel):
    """Stream message model"""

    type: StreamMessageType
    content: Optional[str] = Field(None, description="content of the message")
    data: Optional[dict[str, Any]] = Field(None, description="data object for data message")

    tool_call_id: Optional[str] = Field(None, description="tool call id for tool_call message")
    tool_name: Optional[str] = Field(None, description="tool name for tool_call message")
    tool_input: Optional[dict[str, Any]] = Field(None, description="tool input for tool_call message")
    tool_result: Optional[Any] = Field(None, description="tool result for tool_result message")

    error: Optional[str] = Field(None, description="error for finish message")
    result: Optional[Any] = Field(None, description="result for finish message")

    timestamp: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="timestamp for message"
    )

    tags: Optional[list[str]] = Field(None, description="tags for message")
    metadata: Optional[dict[str, Any]] = Field(None, description="metadata for message")

    step_name: Optional[str] = Field(None, description="step name for step start/end message")

    @classmethod
    def from_exception(cls, exception: Exception | str) -> "StreamMessage":
        return cls(type=StreamMessageType.ERROR, content=str(exception))

    @classmethod
    def from_task_result(cls, result: TaskiqResult[Any]) -> "StreamMessage":
        return cls(
            type=StreamMessageType.FINISH, result=result.return_value, error=str(result.error) if result.error else None
        )

    @classmethod
    def from_thinking(cls, content: str) -> "StreamMessage":
        return cls(type=StreamMessageType.THINKING, content=content)

    @classmethod
    def from_text(cls, content: str) -> "StreamMessage":
        return cls(type=StreamMessageType.TEXT, content=content)

    @classmethod
    def from_data(cls, data: dict[str, Any]) -> "StreamMessage":
        return cls(type=StreamMessageType.DATA, data=data)

    @classmethod
    def from_tool_call(cls, tool_call_id: str, tool_name: str, tool_input: dict[str, Any]) -> "StreamMessage":
        return cls(
            type=StreamMessageType.TOOL_CALL, tool_call_id=tool_call_id, tool_name=tool_name, tool_input=tool_input
        )

    @classmethod
    def from_tool_result(cls, tool_call_id: str, tool_name: str, tool_result: Any) -> "StreamMessage":
        return cls(
            type=StreamMessageType.TOOL_RESULT,
            tool_result=tool_result,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
        )

    @classmethod
    def from_step_start(cls, step_name: str) -> "StreamMessage":
        return cls(type=StreamMessageType.STEP_START, step_name=step_name)

    @classmethod
    def from_step_end(cls, step_name: str, result: Any) -> "StreamMessage":
        return cls(type=StreamMessageType.STEP_END, step_name=step_name, result=result)


def message_to_sse(message: StreamMessage) -> str:
    return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"


async def stream_message_generator(
    message_stream: Union[
        Generator[StreamMessage, None, None], AsyncGenerator[StreamMessage, None], AsyncIterable[StreamMessage]
    ],
) -> AsyncGenerator[str, None]:
    """
    Convert a StreamMessage generator/async generator to Server-Sent Events format.

    Args:
        message_stream: A generator or async generator that yields StreamMessage objects

    Yields:
        Formatted SSE strings in the format: "data: {json}\n\n"
    """
    # Handle sync generator
    if isinstance(message_stream, Generator):
        for message in message_stream:
            yield message_to_sse(message)
    # Handle async generator
    else:
        async for message in message_stream:
            yield message_to_sse(message)


class SseMessageStream(AsyncIterable[str]):
    def __init__(self, coro: AsyncGenerator[StreamMessage, None] | Coroutine[Any, Any, Any]):
        self.queue = asyncio.Queue[StreamMessage | None]()
        self.coro = coro
        self.task = asyncio.create_task(self._run_coro())
        self.task.add_done_callback(lambda x: self.close())

    def __aiter__(self) -> AsyncGenerator[str, None]:
        async def generator() -> AsyncGenerator[str, None]:
            try:
                while True:
                    message = await self.queue.get()
                    if message is None:
                        break
                    yield message_to_sse(message)
            except asyncio.CancelledError:
                logger.info("http request cancelled")
                self.task.cancel()
                return
            except Exception as e:
                logger.error(f"http request closed by error: {e}", exc_info=True)
                self.task.cancel()
                return

        return generator()

    def put(self, message: StreamMessage) -> None:
        self.queue.put_nowait(message)

    def close(self) -> None:
        self.queue.put_nowait(None)

    async def _run_coro(self) -> None:
        with stream_message_callback(self.put):
            if inspect.isasyncgen(self.coro):
                async for message in self.coro:
                    self.put(message)
            else:
                await self.coro


def _assign_name(name: Optional[str], serialized: Optional[dict[str, Any]]) -> str:
    """Assign a name to a run."""
    if name is not None:
        return name
    if serialized is not None:
        if "name" in serialized:
            return serialized["name"]
        if "id" in serialized:
            return serialized["id"][-1]
    return "Unnamed"


def _message_metadata(metadata: Optional[dict[str, Any]]) -> dict[str, Any]:
    # filter out metadata keys that start with 'langgraph'
    metadata = metadata or {}
    filtered_metadata = {k: v for k, v in metadata.items() if (not k.startswith("langgraph") and k != "checkpoint_ns")}
    return filtered_metadata or None


class StreamMessageCallbackHandler(AsyncCallbackHandler):
    """Callback handler that converts LangChain events to StreamMessage objects and calls on_message callback."""

    def __init__(self, on_message: Callable[[StreamMessage], Union[None, Awaitable[None]]], explanation: bool = True):
        """Initialize the callback handler with an on_message callback function.

        Args:
            on_message: Callback function that receives StreamMessage objects.
                       Can be either sync (returns None) or async (returns Awaitable[None])
            explanation: Whether to process explanations from tool_start events. Defaults to True.
        """
        self.on_message = on_message
        self.explanation = explanation
        self.explanations: list[str] = []

    async def on_llm_end(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        **kwargs: Any,
    ) -> None:
        """Run when LLM ends running.

        Args:
            response (LLMResult): The response which was generated.
            run_id (UUID): The run ID. This is the ID of the current run.
            parent_run_id (UUID): The parent run ID. This is the ID of the parent run.
            tags (Optional[list[str]]): The tags.
            kwargs (Any): Additional keyword arguments.
        """
        message: BaseMessage | None = None
        for gen in response.generations:
            for chunk in gen:
                message = chunk.message
                break
        if message and isinstance(message, AIMessage):
            message: AIMessage = cast(AIMessage, message)
            if message.tool_calls:
                if text := message.text():
                    message = StreamMessage.from_thinking(text)
                    message.tags = tags
                    message.metadata = _message_metadata(kwargs.get("metadata", None))
                    await self.handle_message(message)

    async def handle_message(self, message: StreamMessage) -> None:
        """Handle a StreamMessage by calling the on_message callback.

        This method automatically detects whether on_message is sync or async
        using inspect.iscoroutinefunction() and handles it appropriately.

        Args:
            message: The StreamMessage to handle
        """
        try:
            if inspect.iscoroutinefunction(self.on_message):
                # It's an async function
                await self.on_message(message)
            else:
                # It's a sync function
                self.on_message(message)
        except Exception:
            # Handle any errors in callback execution
            pass

    async def on_tool_start(
        self,
        serialized: dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        inputs: Optional[dict[str, Any]] = None,
        name: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
        name = _assign_name(name, serialized)
        if self.explanation and inputs and (explanation := inputs.get("explanation", None)):
            if explanation and isinstance(explanation, str):
                if explanation not in self.explanations:
                    # Send the explanation as a message
                    message = StreamMessage.from_thinking(explanation)
                    message.tags = tags
                    message.metadata = _message_metadata(metadata)

                    await self.handle_message(message)
                    self.explanations.append(explanation)

    async def on_custom_event(
        self,
        name: str,
        data: Any,
        *,
        run_id: UUID,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        if name == "thinking" and isinstance(data, str):
            message = StreamMessage.from_thinking(data)
            message.tags = tags
            message.metadata = _message_metadata(metadata)

            await self.handle_message(message)

        elif name == "data" and isinstance(data, dict):
            message = StreamMessage.from_data(data)
            await self.handle_message(message)

    async def on_chat_model_start(
        self,
        serialized: dict[str, Any],
        messages: list[list[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        self.explanations = []
        pass


stream_callback_var: ContextVar[Optional[StreamMessageCallbackHandler]] = ContextVar("stream_callback", default=None)
register_configure_hook(stream_callback_var, True)


@contextmanager
def stream_message_callback(
    on_message: Callable[[StreamMessage], Union[None, Awaitable[None]]],
) -> Generator[StreamMessageCallbackHandler, None, None]:
    """Get the stream message callback handler in a context manager.

    Returns:
        StreamMessageCallbackHandler: The stream message callback handler.

    Example:
        >>> with stream_message_callback(on_message) as cb:
        ...     # Use the stream message callback handler
    """
    cb = StreamMessageCallbackHandler(on_message)
    stream_callback_var.set(cb)
    yield cb
    stream_callback_var.set(None)
