from contextlib import contextmanager
from contextvars import ContextVar
from typing import Generator, Optional

from langchain_core.tracers.context import register_configure_hook

from server.streaming import StreamMessage, StreamMessageCallbackHandler
from server.task.task_manager import get_current_task_id, task_manager


class TaskCallbackHandler(StreamMessageCallbackHandler):
    def __init__(self, task_id: str | None = None, explanation: bool = True):
        self.task_id = task_id or get_current_task_id()
        assert self.task_id, "task_id is required"

        # Initialize parent class with custom message handler
        super().__init__(on_message=self._send_message_to_task_manager, explanation=explanation)

    async def _send_message_to_task_manager(self, message: StreamMessage) -> None:
        """Send message to task manager instead of using parent's handle_message."""
        await task_manager.send_message(self.task_id, message)


task_callback_var: ContextVar[Optional[TaskCallbackHandler]] = ContextVar("task_callback", default=None)
register_configure_hook(task_callback_var, True)


@contextmanager
def task_callback(task_id: str, explanation: bool = True) -> Generator[TaskCallbackHandler, None, None]:
    """Get the task callback handler in a context manager.
    which conveniently exposes token and cost information.

    Args:
        task_id: The task ID to associate with the callback handler.
        explanation: Whether to process explanations from tool_start events. Defaults to True.

    Returns:
        TaskCallbackHandler: The task callback handler.

    Example:
        >>> with task_callback("task_123") as cb:
        ...     # Use the task callback handler
    """
    cb = TaskCallbackHandler(task_id, explanation=explanation)
    task_callback_var.set(cb)
    yield cb
    task_callback_var.set(None)
