import asyncio
from contextlib import contextmanager
from contextvars import ContextV<PERSON>
from datetime import datetime
from typing import Any, As<PERSON><PERSON><PERSON>ator, Dict, Optional, overload

from langchain_core.callbacks.manager import shielded
from loguru import logger
from pubsub.core import Publisher
from taskiq import Async<PERSON>askiqTask, TaskiqMiddleware, TaskiqR<PERSON>ult
from taskiq.depends.progress_tracker import TaskProgress
from taskiq.kicker import AsyncKicker
from taskiq.message import TaskiqMessage

from server.common_types import MessageType, TaskStatus, TaskType
from server.streaming import StreamMessage, StreamMessageType
from server.task.mongodb_store import mongodb_store

# define context variable for task information
task_id_var: ContextVar[str] = ContextVar("task_id", default="")


@contextmanager
def task_context(task_id: str):
    """context manager to set task context variable and logger contextualize"""
    # set context variable
    task_id_token = task_id_var.set(task_id)

    # contextualize logger with task information
    with logger.contextualize(task_id=task_id):
        try:
            yield
        finally:
            # reset context variable
            task_id_var.reset(task_id_token)


def get_current_task_id() -> str:
    """get current task id from context variable"""
    return task_id_var.get()


class TaskStreamManager(TaskiqMiddleware):
    """
    A middleware that manages task streams and MongoDB storage.

    Combines task streaming functionality with MongoDB persistence.
    """

    def __init__(self):
        super().__init__()

        self.publisher = Publisher()
        self.pending_tasks_data: Dict[str, Any] = {}
        # 集成 MongoDB 存储
        self.mongodb_store = mongodb_store

    # TaskiqMiddleware API
    async def pre_send(self, message: TaskiqMessage) -> TaskiqMessage:
        """任务发送前调用"""
        task_id = message.task_id
        labels = message.labels or {}

        # 检查任务是否已存在
        existing_task = self.mongodb_store.get_task(task_id)
        if not existing_task:
            self.mongodb_store.create_task(task_id, tags=labels)
        return message

    async def post_send(self, message: TaskiqMessage) -> TaskiqMessage:
        """任务发送后调用"""
        task_id = message.task_id

        # 创建初始的 task_status 记录
        await self._save_task(message)

        # 原有功能
        await self.set_progress(task_id, TaskStatus.PENDING)
        return message

    async def pre_execute(self, message: TaskiqMessage) -> TaskiqMessage:
        """任务执行前调用"""
        task_id = message.task_id

        # 更新任务状态为运行中，并设置开始时间
        now = datetime.now()
        self.mongodb_store.task_collection.update_one(
            {"_id": task_id}, {"$set": {"status": TaskStatus.RUNNING, "started_at": now, "updated_at": now}}
        )

        # 原有功能
        await self.set_progress(task_id, TaskStatus.RUNNING)
        return message

    @shielded
    async def post_execute(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """任务执行后调用"""
        task_id = message.task_id
        task_name = message.task_name.split(":", 1)[-1] if ":" in message.task_name else message.task_name
        status = TaskStatus.COMPLETED if not result.is_err else TaskStatus.FAILED
        logger.info(
            "task {task_name}/{task_id} completed with status: {status}{error}",
            task_name=task_name,
            task_id=task_id,
            status=status.value,
            error=f" {result.error}" if result.is_err else "",
        )

        # 更新 MongoDB 中的任务状态和结果
        self.mongodb_store.save_task_result(
            task_id=task_id,
            status=status,
            result=result.return_value,
            error=str(result.error) if result.is_err else None,
        )

        await self.send_message(task_id, StreamMessage.from_task_result(result))
        await self.set_progress(task_id, status)

        # unsubscribe all subscribers from this task
        self.publisher.unsubAll(topicName=task_id)

    def on_error(
        self,
        message: "TaskiqMessage",
        result: "TaskiqResult[Any]",
        exception: BaseException,
    ) -> None:
        task_name = message.task_name.split(":", 1)[-1] if ":" in message.task_name else message.task_name
        logger.error(f"task {task_name} error: {exception}")

    async def _save_task(self, message: TaskiqMessage) -> None:
        """创建初始的任务状态记录"""
        try:
            task_id = message.task_id
            labels = message.labels or {}

            # 检查任务是否已存在
            existing_task = self.mongodb_store.get_task(task_id)
            if not existing_task:
                self.mongodb_store.create_task(task_id, labels)
        except Exception as e:
            logger.error("创建初始任务状态失败: {}", str(e), exc_info=True)

    async def cancel_task_by_id(self, task_id: str) -> None:
        """
        Cancel a task by id.
        """
        # 根据 task_id 在 mongodb 状态中间件中查找任务
        task = await self.get_async_task(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")

        # 找到之后将任务标记为 CANCELLED
        await self.set_progress(task_id=task_id, status=TaskStatus.CANCELED)

    async def list_incompleted_tasks(self, task_type: str, tags: Dict[str, Any]) -> list[str]:
        """
        根据 task_type 和 tags 列出未完成的任务
        """
        # 使用 MongoDB 存储查询未完成任务
        return self.mongodb_store.list_incompleted_tasks(task_type=task_type, tags=tags)

    async def list_all_tasks(self, tags: Dict[str, Any]) -> list[Dict[str, Any]]:
        """
        根据 tags 列出所有任务
        """
        return self.mongodb_store.list_all_tasks(tags)

    async def get_task_result_by_account_id(
        self, account_id: str, task_type: Optional[str] = TaskType.DETECT_CONTACTS.value
    ) -> Any | None:
        """
        Get the result of a task by account id.
        """
        tags = {"account_id": account_id, "task_type": task_type}
        # 优先从 MongoDB 存储获取任务状态
        store_data = self.mongodb_store.get_task_status_by_tags(tags=tags)
        if store_data:
            return store_data

        # 如果没有找到，直接返回 None，不再尝试访问可能未初始化的 broker
        return None

    async def get_tasks_by_account_ids(
        self, account_ids: list[str], task_type: Optional[str] = TaskType.DETECT_CONTACTS.value
    ) -> list[Dict[str, Any]]:
        """
        Get tasks by multiple account ids.
        Only returns tasks that exist, skips account_ids with no tasks.
        """
        return self.mongodb_store.get_task_status_by_account_ids(account_ids=account_ids, task_type=task_type)

    async def get_task_data(self, task_id: str) -> Any | None:
        """
        Get the data of a task by id.
        """
        # 优先从 MongoDB 存储获取任务数据
        return self.mongodb_store.get_task(task_id=task_id)

    def build_task_finished_messages(self, result: TaskiqResult[Any]) -> list[dict[str, Any]]:
        """构建任务结束消息"""
        messages = []

        if result.is_err:
            messages.append({"type": MessageType.ERROR, "error": result.error})
        else:
            messages.append({"type": MessageType.COMPLETED, **result.return_value})

        messages.append({"type": MessageType.COMPLETED})

        return messages

    async def get_async_task(self, task_id: str) -> AsyncTaskiqTask[Any] | None:
        """
        Get an async task by id.
        """
        # 首先检查任务是否存在于 MongoDB 存储中
        task_data = self.mongodb_store.get_task(task_id=task_id)
        if not task_data:
            return None

        # 直接使用 AsyncTaskiqTask
        task = AsyncTaskiqTask(task_id, result_backend=self.broker.result_backend)  # type: ignore
        return task

    async def subscribe(self, task_id: str) -> AsyncGenerator[StreamMessage, None]:
        """
        订阅特定任务的消息队列

        Args:
            task_id: 任务ID

        Returns:
            AsyncGenerator: 异步生成器，生成订阅的消息
        """
        task_data = self.mongodb_store.get_task(task_id=task_id)
        if not task_data:
            raise Exception(f"Task {task_id} not found")

        queue = asyncio.Queue()

        def callback(message: StreamMessage) -> None:
            queue.put_nowait(message)

        logger.debug("subscribed to task with id: {}", task_id)
        messages = task_data.get("messages", [])
        for message in messages:
            queue.put_nowait(StreamMessage.model_validate(message))

        self.publisher.subscribe(callback, task_id)

        try:
            # 从订阅者队列读取消息并生成
            while True:
                try:
                    # 增加超时机制，防止无限等待
                    message = await asyncio.wait_for(queue.get(), timeout=600)
                    yield message

                    # task completed or cancelled
                    if message.type == StreamMessageType.FINISH:
                        break

                except asyncio.TimeoutError:
                    logger.warning("task {} subscription wait timeout.", task_id)
                    break
        finally:
            # 清理订阅者
            self.publisher.unsubscribe(callback, task_id)

    @overload
    async def send_message(self, task_id: str, *messages: StreamMessage) -> None:
        """Send message to a specific task"""
        ...

    @overload
    async def send_message(self, *messages: StreamMessage) -> None:
        """Send message to the current task"""
        ...

    async def send_message(self, *args: StreamMessage | str) -> None:
        """
        向任务队列中添加消息
        """
        # 解析参数
        task_id = None
        messages = []

        if args and isinstance(args[0], str):
            task_id = args[0]
            messages = args[1:]
        elif args and isinstance(args[0], StreamMessage):
            messages = args
            task_id = get_current_task_id()
            if not task_id:
                logger.warning("task_id not provided and not found in context, skip sending message")
                return
        else:
            raise TypeError("Invalid arguments")

        # send messages
        for message in messages:
            if not isinstance(message, StreamMessage):
                raise TypeError(f"expected StreamMessage, got {type(message)}")
            if message.type == StreamMessageType.THINKING:
                logger.debug(
                    f"thinking: {message.content} {f'metadata: {message.metadata}' if message.metadata else ''}"
                )
            else:
                logger.debug("send message: {}", message.model_dump_json(exclude_none=True))
            self.publisher.sendMessage(task_id, message=message)
            self.mongodb_store.save_task_message(task_id, message)

    async def set_progress(self, task_id: str, status: TaskStatus) -> None:
        """
        Set the progress of the special task.

        Args:
            task_id (str): The id of the task
            status (TaskStatus): The status of the task
        """
        try:
            await self.broker.result_backend.set_progress(
                task_id=task_id, progress=TaskProgress(state=status.value, meta={})
            )
        except Exception as e:
            logger.error("set task progress failed: {}", str(object=e), exc_info=True)

    async def _create_task(
        self,
        task_type: TaskType,
        account_id: str,
        task_func: Optional[Any] = None,
        **kwargs,
    ) -> AsyncTaskiqTask[str]:
        """Create a task."""
        import uuid

        task_id = str(uuid.uuid4()).replace("-", "")

        # 构建 labels，包含所有需要存储的信息
        labels: Dict[str, Any] = {
            "task_type": task_type.value,
            "account_id": account_id,
            **kwargs,
        }

        if not task_func:
            raise ValueError("task_func must be provided")

        kicker: AsyncKicker = task_func.kicker().with_task_id(task_id).with_labels(**labels)

        # 保持原有的参数结构
        task: AsyncTaskiqTask[str] = await kicker.kiq(
            account_id=account_id,
            **kwargs,
        )
        return task

    async def add_detect_contact_task(
        self, account_id: str, owner_id: Optional[str] = None, **kwargs
    ) -> AsyncTaskiqTask[str]:
        """
        Add a detect contact task to the task stream.
        """
        from server.task.taskiq_app import contacts_discovery_processor_task

        return await self._create_task(
            task_type=TaskType.DETECT_CONTACTS,
            account_id=account_id,
            owner_id=owner_id,
            task_func=contacts_discovery_processor_task,
            **kwargs,
        )

    async def add_lookalike_companies_task(self, account_id: str, **kwargs) -> AsyncTaskiqTask[str]:
        """
        Add a lookalike companies task to the task stream.
        """
        from server.task.taskiq_app import lookalike_companies_task

        return await self._create_task(
            task_type=TaskType.LOOKALIKE_COMPANIES,
            account_id=account_id,
            task_func=lookalike_companies_task,
            **kwargs,
        )

    async def store_task_data(self, task_id: str, message_payload: Dict[str, Any]) -> None:
        """
        存储任务数据到 result_backend

        Args:
            task_id: 任务ID
            message_payload: 消息载荷
        """
        try:
            if hasattr(self.broker, "result_backend") and hasattr(self.broker.result_backend, "set_task_data"):
                await self.broker.result_backend.set_task_data(task_id=task_id, message_payload=message_payload)  # type: ignore
        except Exception as e:
            logger.error("存储任务数据失败: {}", str(e), exc_info=True)


# create global task manager instance
task_manager = TaskStreamManager()
