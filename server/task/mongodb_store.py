from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional

from loguru import logger
from pymongo import ASCENDING, DESCENDING
from pymongo.collection import Collection

from config import (
    MONGODB_RESULTS_COLLECTION_NAME,
)
from server.common_types import TaskStatus
from server.streaming import StreamMessage
from server.task.mongodb_client import mongodb_client


class MongoDBStore:
    """MongoDB 数据存储类，负责所有与 MongoDB 相关的数据操作"""

    def __init__(
        self,
        task_collection_name: str = MONGODB_RESULTS_COLLECTION_NAME,
    ):
        """初始化 MongoDB 存储"""
        self.db = mongodb_client.db
        self.task_collection: Collection = self.db[task_collection_name]
        self._setup_indexes()

    def _setup_indexes(self) -> None:
        """设置必要的索引"""
        try:
            # 定义需要创建的索引列表
            indexes_to_create = [
                {"keys": [("tags.account_id", ASCENDING)], "name": "account_id_idx", "background": True},
                {"keys": [("status", ASCENDING)], "name": "task_status_idx", "background": True},
                {"keys": [("type", ASCENDING)], "name": "task_type_idx", "background": True},
                {"keys": [("created_at", DESCENDING)], "name": "created_at_desc_idx", "background": True},
            ]
            # 使用 create_index 创建索引
            for index in indexes_to_create:
                self.task_collection.create_index(index["keys"], name=index["name"], background=index["background"])

        except Exception as e:
            logger.error("创建索引失败: {}", str(e), exc_info=True)
            raise

    def create_task(self, task_id: str, tags: Dict[str, Any]) -> None:
        """创建初始的任务记录"""
        try:
            now = datetime.now()
            # 构建初始任务数据
            task_data = {
                "_id": task_id,
                "status": TaskStatus.PENDING,
                "type": tags.get("task_type", ""),
                "created_at": now,
                "updated_at": now,
                "tags": tags,
            }

            self.task_collection.insert_one(task_data)
            logger.debug("初始任务记录已创建: {}", task_id)

        except Exception as e:
            logger.error("创建初始任务记录失败: {}", str(e), exc_info=True)

    def save_task_message(self, task_id: str, message: StreamMessage) -> None:
        """添加任务事件到 events 数组"""
        try:
            self.task_collection.update_one(
                {"_id": task_id},
                {"$push": {"messages": message.model_dump(exclude_none=True)}},
            )
        except Exception as e:
            logger.error("添加任务消息失败: {}", str(e), exc_info=True)

    def add_task_event(
        self, task_id: str, event_type: str, event_name: str, details: Optional[Dict[str, Any]] = None
    ) -> None:
        """添加任务事件到 events 数组"""
        try:
            now = datetime.now()

            event = {"type": event_type, "name": event_name, "ts": now, "details": details or {}}

            # 添加事件到 events 数组并更新 updated_at
            self.task_collection.update_one({"_id": task_id}, {"$push": {"events": event}, "$set": {"updated_at": now}})
            logger.debug("任务事件已添加: {} - {}: {}", task_id, event_type, event_name)

        except Exception as e:
            logger.error("添加任务事件失败: {}", str(e), exc_info=True)

    def save_task_result(
        self,
        task_id: str,
        status: TaskStatus,
        event: dict | None = None,
        result: dict | None = None,
        error: str | None = None,
    ) -> None:
        """存储任务状态数据"""
        try:
            now = datetime.now()

            update = {}

            # 更新任务状态
            set_fields = {"status": status, "updated_at": now, "error": error, "completed_at": now}
            if error:
                set_fields["error"] = error
            else:
                set_fields["result"] = result

            if event:
                # 如果包含事件，直接追加到 events 数组
                event_data = event
                event_data = {
                    "type": event_data.get("type", ""),
                    "name": event_data.get("name", ""),
                    "ts": now,
                    "details": event_data.get("details", {}),
                }

                update["$push"] = {"events": event_data}

                logger.debug("任务事件已追加: {} - {}: {}", task_id, event["type"], event["name"])

            update["$set"] = set_fields

            self.task_collection.update_one({"_id": task_id}, update)

        except Exception as e:
            logger.error("存储任务状态数据失败: {}", str(e), exc_info=True)

    def list_incompleted_tasks(self, task_type: str, tags: Dict[str, Any]) -> List[str]:
        """根据任务类型和标签列出未完成的任务"""
        try:
            query = {"type": task_type, "status": {"$nin": ["completed", "failed", "canceled"]}}

            # 添加 tags 查询条件
            for key, value in tags.items():
                query[f"tags.{key}"] = value

            logger.debug("查询未完成任务，查询条件: {}", query)

            # 按创建时间降序排序，最新的任务在前
            tasks = self.task_collection.find(query, {"_id": 1}).sort("created_at", -1)
            result = [task["_id"] for task in tasks if task.get("_id")]
            logger.info("查询到 {} 个未完成任务: {}", len(result), result)
            return result
        except Exception as e:
            logger.error("查询未完成任务失败: {}", str(e), exc_info=True)
            return []

    def list_all_tasks(self, tags: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据标签查询所有任务"""
        try:
            # 构建查询条件，支持部分匹配
            query = {}
            for key, value in tags.items():
                query[f"tags.{key}"] = value

            # 按创建时间降序排序，最新的任务在前
            tasks = self.task_collection.find(query).sort("created_at", -1)
            if not tasks:
                return []

            tasks = list(tasks)
            for task in tasks:
                task["_id"] = str(task.get("_id", ""))  # 将 _id 转换为字符串
            return tasks
        except Exception as e:
            logger.error("查询任务失败: {}", str(e), exc_info=True)
            return []

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """根据任务ID获取单个任务"""
        try:
            task = self.task_collection.find_one({"_id": task_id})
            if task:
                logger.debug("找到任务: {}", task_id)
            else:
                logger.debug("未找到任务: {}", task_id)
            return task
        except Exception as e:
            logger.error("获取任务失败: {}", str(e), exc_info=True)
            return None

    def get_task_status_by_tags(self, tags: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """根据标签获取任务状态"""
        try:
            # 构建查询条件，支持部分匹配
            query = {}
            for key, value in tags.items():
                query[f"tags.{key}"] = value

            task = self.task_collection.find_one(
                query,
                sort=[("created_at", -1)],  # 添加排序以保持一致性
            )
            return task
        except Exception as e:
            logger.error("获取任务状态失败: {}", str(e), exc_info=True)
            return None

    def get_task_status_by_account_ids(
        self, account_ids: List[str], task_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """根据账户ID列表批量获取任务状态"""
        try:
            # 构建查询条件
            query = {"tags.account_id": {"$in": account_ids}}
            if task_type:
                query["tags.task_type"] = task_type

            # 按创建时间降序排序，获取每个账户的最新任务
            tasks = self.task_collection.find(query, projection={"messages": 0}).sort("created_at", -1)
            if not tasks:
                return []

            # 去重，每个账户只保留最新的任务
            account_tasks = {}
            for task in tasks:
                account_id = task.get("tags", {}).get("account_id")
                if account_id and account_id not in account_tasks:
                    account_tasks[account_id] = task

            result = list(account_tasks.values())
            for task in result:
                task["_id"] = str(task.get("_id", ""))  # 将 _id 转换为字符串

            return result
        except Exception as e:
            logger.opt(exception=e).error("批量获取任务状态失败: {}", str(e))
            return []

    def update_task_status(self, task_id: str, status: TaskStatus) -> None:
        """更新任务状态"""
        try:
            now = datetime.now()
            update_data = {"status": status, "updated_at": now}

            # 如果任务状态变为运行中，设置开始时间
            if status == TaskStatus.RUNNING:
                update_data["started_at"] = now

            # 如果任务完成，设置完成时间
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELED]:
                update_data["completed_at"] = now

            result = self.task_collection.update_one({"_id": task_id}, {"$set": update_data})
            if result.modified_count > 0:
                logger.debug("任务状态已更新: {} - {}", task_id, status)
            else:
                logger.warning("任务状态更新失败，未找到任务: {}", task_id)
        except Exception as e:
            logger.error("更新任务状态失败: {}", str(e), exc_info=True)

    def mark_task_as_cancelled(self, task_id: str) -> None:
        """将任务标记为已取消状态"""
        try:
            now = datetime.now()

            # 更新任务状态
            self.task_collection.update_one(
                {"_id": task_id}, {"$set": {"status": TaskStatus.CANCELED, "updated_at": now}}
            )
            logger.debug("任务已标记为取消: {}", task_id)
        except Exception as e:
            logger.error("标记任务为取消状态失败: {}", str(e), exc_info=True)

    def find_timeout_tasks(self, timeout_minutes: int) -> List[Dict[str, Any]]:
        """查找超时的运行中或待处理任务"""
        try:
            # 计算超时时间点
            timeout_threshold = datetime.now() - timedelta(minutes=timeout_minutes)

            # 查询条件：状态为 running ， 且 started_at 超过阈值
            query = {
                "status": {"$in": [TaskStatus.RUNNING]},
                "started_at": {"$lt": timeout_threshold},
            }

            tasks = list(self.task_collection.find(query))
            logger.info("找到 {} 个超时任务", len(tasks))
            return tasks

        except Exception as e:
            logger.error("查找超时任务失败: {}", str(e), exc_info=True)
            return []

    def mark_task_as_timeout(self, task_id: str) -> None:
        """将任务标记为超时失败"""
        try:
            now = datetime.now()

            # 添加超时事件
            self.add_task_event(task_id, "error", "任务执行超时", {"source": ["task_cleanup"], "error": "timeout"})

            # 更新任务状态和结果
            update_data = {
                "status": "failed",
                "completed_at": now,
                "updated_at": now,
                "result": {"error": "timeout", "status": "failed", "completed_at": now.isoformat()},
            }

            result = self.task_collection.update_one({"_id": task_id}, {"$set": update_data})

            if result.modified_count > 0:
                logger.info("任务已标记为超时: {}", task_id)
            else:
                logger.warning("标记任务超时失败，未找到任务: {}", task_id)

        except Exception as e:
            logger.error("标记任务超时失败: {}", str(e), exc_info=True)


# 创建全局实例
mongodb_store = MongoDBStore()
