from typing import AsyncGenerator
from urllib.parse import urlparse

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from langfuse import get_client
from loguru import logger
from pydantic import BaseModel, Field

from agent.search_people import agentic_search_people
from agent.tools.apollo import apollo
from server.streaming import SseMessageStream, StreamMessage, StreamMessageType
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.contacts_api import add_contacts_to_zoho_use_origin_api
from zoho.data_transform import safe_str, transform_apollo_contact_data_to_zoho_contact_data
from zoho.get_existing_contacts import get_existing_contacts

router = APIRouter(tags=["contacts"])


def normalize_linkedin_url(url: str) -> str:
    """
    Normalize LinkedIn URL by removing protocol, www prefix, and converting to lowercase.

    Args:
        url: The LinkedIn URL to normalize

    Returns:
        Normalized URL without a protocol and www prefix
    """
    if not url:
        return ""

    # Convert to lowercase first
    url = url.lower()

    # Add protocol if missing to help urlpar<PERSON> work correctly
    if not url.startswith(("http://", "https://", "//")):
        url = f"https://{url}"

    # Parse the URL and extract the netloc + path
    parsed = urlparse(url)

    # Remove www prefix from domain
    domain = parsed.netloc
    if domain.startswith("www."):
        domain = domain[4:]

    # Return domain + path, removing trailing slash
    return f"{domain}{parsed.path}".rstrip("/")


class SmartAddContactRequest(BaseModel):
    user_input: str = Field(..., description="用户查询的联系人信息")
    account_id: str = Field(..., description="Zoho 账户 ID，用于获取公司信息")
    user_info: dict = Field(..., description="用户信息，包含 username 和 email 等字段")
    request_headers: dict = Field(..., description="请求头信息")


async def _handle_contact_creation_flow(request: SmartAddContactRequest) -> AsyncGenerator[StreamMessage, None]:
    langfuse_client = get_client()
    user_input = request.user_input
    account_id = request.account_id
    request_headers = request.request_headers

    with langfuse_client.start_as_current_span(
        name="smart_add_contact",
        input=user_input,
        metadata={
            "account_id": account_id,
            "user_info": request.user_info,
        },
    ) as langfuse_trace_span:
        try:
            langfuse_client.update_current_trace(user_id=request.user_info.get("email"))

            yield StreamMessage.from_thinking(
                content="We are searching for contacts based on your input. Please wait.",
            )

            zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
            if not zoho_account_info:
                yield StreamMessage.from_exception(
                    Exception(f"Cannot find Zoho account info for account_id: {account_id}")
                )
                return

            company_name = zoho_account_info.get("name")
            if not company_name:
                yield StreamMessage.from_exception(
                    Exception(f"Missing company name in Zoho account info for account_id: {account_id}")
                )
                return

            enhanced_query = f"Find people at {company_name} who match: {user_input}"

            searched_person = await agentic_search_people(enhanced_query)

            no_find_error_msg = f"No matching contacts were found in {company_name}."
            if not searched_person or not searched_person.profile_url:
                yield StreamMessage.from_exception(Exception(no_find_error_msg))
                return

            yield StreamMessage.from_thinking(
                content="We have found a contact and are checking whether it already exists in the CRM.",
            )

            existing_contacts = await get_existing_contacts(company_name, account_id)

            if existing_contacts and len(existing_contacts) > 0:
                existing_contact = next(
                    (
                        contact
                        for contact in existing_contacts
                        if normalize_linkedin_url(contact.get("linkedin_url", ""))
                        == normalize_linkedin_url(searched_person.profile_url)
                    ),
                    None,
                )

                if existing_contact:
                    existing_error_msg = StreamMessage.from_exception(
                        Exception(f"The contact {searched_person.full_name} already exists in {company_name}.")
                    )
                    existing_error_msg.data = existing_contact
                    yield existing_error_msg
                    return

            yield StreamMessage.from_thinking(
                content=f"We are completing the {searched_person.full_name}’s information. Please wait.",
            )

            enriched_contacts = await apollo.enrich_person(
                linkedin_url=searched_person.profile_url,
            )

            if not enriched_contacts:
                yield StreamMessage.from_exception(Exception(no_find_error_msg))
                return

            yield StreamMessage.from_thinking(
                content=f"The contact({searched_person.full_name}) information has been completed and is being added to the CRM.",  # noqa: E501
            )

            cleaned_contact = {key: safe_str(value) for key, value in enriched_contacts.items()}

            # transform data
            transform_zoho_contact = await transform_apollo_contact_data_to_zoho_contact_data(
                apollo_contact_data=cleaned_contact, default_data={}
            )

            success_contacts = await add_contacts_to_zoho_use_origin_api(
                account_id=account_id,
                contacts=[transform_zoho_contact],
                request_headers=request_headers,
                owner_id=zoho_account_info.get("owner", {}).get("id"),
            )

            if success_contacts and len(success_contacts) > 0:
                finish_msg = StreamMessage.from_data(success_contacts[0])
                finish_msg.type = StreamMessageType.FINISH
                yield finish_msg
                langfuse_trace_span.update(output=finish_msg.data)
            else:
                error_msg = StreamMessage.from_exception(Exception(no_find_error_msg))
                yield error_msg
                langfuse_trace_span.update(output={"error": error_msg})

            return

        except Exception as e:
            logger.error(f"Error in smart_add_contact: {e}", exc_info=True)
            yield StreamMessage.from_exception(e)


@router.post("/contact/smart-add")
async def smart_add_contact(request: SmartAddContactRequest):
    """
    Smartly adds a contact based on the user's query and account ID.
    """

    return StreamingResponse(
        content=SseMessageStream(_handle_contact_creation_flow(request)),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )
