from typing import Any, Dict

from langfuse import get_client
from loguru import logger

from agent.account.lookalike_companies_workflow import execute_lookalike_companies_workflow
from server.task.base_task_processor import BaseTaskProcessor
from zoho.accounts_api import fetch_accounts_info_by_account_id


class LookalikeCompaniesProcessor(BaseTaskProcessor):
    def __init__(self, task_id: str):
        super().__init__(task_id=task_id, task_name="lookalike_companies")

    async def _arun(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        探查任务执行入口

        Args:
            data: 输入数据，包含 account_id, user_query, excludes 和 current_user

        Returns:
            Dict[str, Any]: 处理结果
        """
        account_id = data.get("account_id")
        if not account_id:
            raise ValueError("missing account_id in input data")

        user_query = data.get("user_query")
        if not user_query:
            raise ValueError("missing user_query in input data")

        langfuse = get_client()
        langfuse.update_current_trace(
            user_id=data.get("current_user", {}).get("email"),
            metadata={
                "account_id": account_id,
            },
        )

        # Query account info from zoho
        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
        if not zoho_account_info:
            raise ValueError(f"cannot find zoho account info for account_id: {account_id}")

        excludes = data.get("excludes", [])
        logger.info(
            f"Lookalike companies processor started. user_query: {user_query}, zoho_account_info: {zoho_account_info}, excludes: {excludes}"  # noqa: E501
        )

        # 执行探查agent
        return await execute_lookalike_companies_workflow(
            user_query=user_query,
            zoho_account_info=zoho_account_info,
            excludes=excludes,
            ideal_customer_profile=data.get("ideal_customer_profile"),
            apollo_company_info=data.get("apollo_company_info"),
        )
