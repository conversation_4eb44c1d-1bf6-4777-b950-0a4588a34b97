import asyncio
from typing import AsyncGenerator

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from langfuse import get_client
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.account_enrichment import get_enriched_account_info
from agent.account.models import SearchCompaniesResult
from agent.account.search_target_companies import search_target_companies
from server.account.add_account_to_zoho import add_account_to_crm_zoho
from server.account.lookalike_companies_thread_manager import lookalike_companies_thread_manager
from server.common_types import CurrentUser
from server.streaming import (
    SseMessageStream,
    StreamMessage,
    StreamMessageType,
)
from zoho.accounts_api import AccountExistsException

router = APIRouter(tags=["account"])


class LookalikeCompaniesRequest(BaseModel):
    account_id: str = Field(..., description="The ID of the account")
    user_query: str = Field(..., description="The user_query to use for the account")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class LookalikeCompaniesMoreRequest(BaseModel):
    thread_id: str = Field(..., description="The thread ID")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class AddAccountRequest(BaseModel):
    user_id: str = Field(..., description="The user ID")
    account_info: dict = Field(..., description="The account info")
    request_headers: dict = Field(..., description="The request headers")


@router.post("/prospecting-accounts")
@router.post("/lookalike-companies")
async def lookalike_companies(request: LookalikeCompaniesRequest):
    """
    Start a new lookalike companies thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    account_id = request.account_id
    user_query = request.user_query
    current_user = request.current_user

    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    if not user_query:
        raise HTTPException(status_code=400, detail="User query is required")

    try:
        # create lookalike companies thread
        return await lookalike_companies_thread_manager.create_thread(
            account_id=account_id,
            user_query=user_query,
            current_user=current_user,
        )
    except Exception as e:
        logger.error(f"Create lookalike companies thread failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/prospecting-accounts/more")
@router.post("/lookalike-companies/more")
async def lookalike_companies_more(request: LookalikeCompaniesMoreRequest):
    """
    Lookalike companies more on the existing thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    thread_id = request.thread_id
    current_user = request.current_user
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")

    try:
        # add new lookalike companies task to thread
        return await lookalike_companies_thread_manager.add_thread_task(thread_id, current_user)
    except Exception as e:
        logger.error(f"Add lookalike companies thread task failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/prospecting-accounts/{account_id}/threads")
@router.get("/lookalike-companies/{account_id}/threads")
async def get_lookalike_companies_history(account_id: str):
    """
    Get the history of lookalike companies
    """
    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    return await lookalike_companies_thread_manager.list_all_lookalike_companies_threads(account_id)


@router.get("/prospecting-accounts/thread/{thread_id}")
@router.get("/lookalike-companies/thread/{thread_id}")
async def get_lookalike_companies_task_profile(thread_id: str):
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")
    try:
        return await lookalike_companies_thread_manager.get_lookalike_companies_thread_data(thread_id)
    except Exception as e:
        logger.error(f"Get lookalike companies data failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/add-account")
async def add_account(request: AddAccountRequest):
    """Add a account to crm zoho"""
    logger.debug(f"收到添加account请求，request: {request.model_dump()}")

    user_id = request.user_id
    account_info = request.account_info
    request_headers = request.request_headers

    if not user_id:
        raise HTTPException(status_code=400, detail="User ID is required")

    if not account_info:
        raise HTTPException(status_code=400, detail="Account info is required")

    langfuse = get_client()
    with langfuse.start_as_current_span(name="add_account_to_crm", input=request.model_dump()) as span:
        try:
            # 添加公司到 crm zoho
            account_id = await add_account_to_crm_zoho(
                account_info=account_info, user_id=user_id, request_headers=request_headers
            )
            result = {"account_id": account_id}

            span.update(output=result, level="INFO")
            return result
        except (AccountExistsException, Exception) as e:
            span.update(status_message=str(e), level="ERROR")
            raise HTTPException(status_code=400, detail=str(e))


class AddAccountByUserInputRequest(BaseModel):
    user_input: str = Field(..., description="The user input")
    user_id: str = Field(..., description="The current user id")
    request_headers: dict = Field(..., description="The request headers")
    user_info: dict = Field(default_factory=dict, description="The user info")


async def _handle_account_creation_flow(request: AddAccountByUserInputRequest) -> AsyncGenerator[StreamMessage, None]:
    """Handle the complete account creation flow and stream messages for each step."""
    try:
        user_input = request.user_input
        user_id = request.user_id
        user_info = request.user_info
        request_headers = request.request_headers

        if not user_input:
            yield StreamMessage.from_exception(Exception("User input is required"))
            return

        if not user_id:
            yield StreamMessage.from_exception(Exception("User ID is required"))
            return

        if not request_headers:
            yield StreamMessage.from_exception(Exception("Request headers is required"))
            return

        yield StreamMessage.from_thinking("Let me search for companies first...")

        # 搜索公司信息
        companies_result: SearchCompaniesResult = await search_target_companies(user_input, user_info)

        if not companies_result.companies:
            # 搜索不到结果
            yield StreamMessage.from_exception(companies_result.error or "not_found")
            return

        elif len(companies_result.companies) > 1:
            # 搜索到多个结果
            yield StreamMessage(
                type=StreamMessageType.FINISH,
                data={
                    "result": [company.model_dump(exclude_none=True) for company in companies_result.companies],
                },
            )

            return

        account_info = companies_result.companies[0]

        # 发送找到公司信息的消息
        yield StreamMessage.from_thinking(
            f"The target company is: Name: {account_info.name}, Website: {account_info.website}"
        )

        # 开始添加到CRM
        yield StreamMessage.from_thinking("Now I'm creating the account in the CRM system...")

        try:
            # 添加公司到 crm zoho
            account_id = await add_account_to_crm_zoho(
                account_info=account_info.model_dump(exclude_none=True),
                user_id=user_id,
                request_headers=request_headers,
            )

            # 发送成功消息
            yield StreamMessage.from_thinking(f"The company has been added to CRM. The account ID is: {account_id}")

            # 发送完成消息
            yield StreamMessage(
                type=StreamMessageType.FINISH,
                data={"account_id": account_id},
            )

        except AccountExistsException as e:
            yield StreamMessage(
                type=StreamMessageType.ERROR,
                content="exists",
                data={"account_id": e.account_id, "name": account_info.name},
            )
            return

        except Exception as e:
            logger.error(f"Add account to crm zoho failed: {e}", exc_info=True)
            yield StreamMessage.from_exception(Exception("unknown_error"))

    except asyncio.CancelledError:
        logger.info("Stream cancelled")
        return
    except Exception as e:
        logger.error(f"Stream error: {e}", exc_info=True)
        yield StreamMessage.from_exception("unknown_error")


@router.post("/add-account-by-user-input")
async def add_account_by_user_input(request: AddAccountByUserInputRequest):
    """Add an account to crm zoho by user input with SSE streaming"""

    return StreamingResponse(
        content=SseMessageStream(_handle_account_creation_flow(request)),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )


@router.get("/account/{account_id}/enrich")
async def enrich_account(account_id: str, username: str = None, email: str = None):
    """
    Enrich account information
    """
    try:
        return await get_enriched_account_info(account_id, username, email)
    except Exception as e:
        logger.error(f"Enrich account {account_id} failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail={"error": "failed", "messages": "Failed to enrich account"})
