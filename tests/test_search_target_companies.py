from agent.account.models import SearchCompaniesResult
from agent.account.search_target_companies import search_target_companies
from utils.logging_callback_handler import logging_callback


class TestSearchTargetCompanies:
    """测试 search_target_companies 功能的测试用例"""

    async def test_search_target_companies_success(self):
        """测试成功搜索公司的场景"""
        # 执行测试
        with logging_callback():
            result = await search_target_companies("Telsim，澳大利亚电信运营商;\n Kaishan Australia")

        # 验证结果
        assert isinstance(result, SearchCompaniesResult)
        assert len(result.companies) >= 0
        assert result.error is None or isinstance(result.error, str)

        for company in result.companies:
            print(f"- {company.name}, {company.website}")

    async def test_search_target_companies_no_results(self):
        """测试没有找到公司结果的场景"""
        result = await search_target_companies("不存在的公司名称")

        assert isinstance(result, SearchCompaniesResult)
        assert len(result.companies) == 0
        assert result.error is not None

    async def test_search_target_companies_strange_input(self):
        """测试奇怪的输入"""
        result = await search_target_companies("123")
        assert isinstance(result, SearchCompaniesResult)
        assert len(result.companies) >= 0
        assert result.error is None

    async def test_search_target_companies_multiple_results(self):
        """测试返回多个公司结果的场景"""
        result = await search_target_companies("澳大利亚电信运营商")

        assert isinstance(result, SearchCompaniesResult)
        assert len(result.companies) >= 0
        assert result.error is None or isinstance(result.error, str)

        for company in result.companies:
            print(f"- {company.name}, {company.website}")

    async def test_search_target_companies_fuzzy_match(self):
        """测试模糊匹配场景"""
        result = await search_target_companies("Robertson")

        assert isinstance(result, SearchCompaniesResult)
        assert len(result.companies) >= 0
        assert result.error is None or isinstance(result.error, str)
        for company in result.companies:
            print(f"- {company.name}, {company.website}")
