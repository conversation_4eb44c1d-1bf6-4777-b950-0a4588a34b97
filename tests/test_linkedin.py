from pprint import pprint

import yaml

from agent.tools.linkedin import get_company_details
from utils.logging_callback_handler import logging_callback


async def test_get_company_details():
    with logging_callback():
        results = await get_company_details.coroutine(domains=["www.thinaer.io"], usernames=["thinaerxxxx"])
        print(results)
        results = yaml.safe_load(results)

        pprint(results[0])
        assert results is not None
        assert len(results) == 2
        assert results[0].get("name") is not None
        assert results[0].get("description") is not None
        assert results[0].get("website") is not None
