from dotenv import load_dotenv

from common.prompts import inhand_business_card
from research.website_research.website_research_agent import WebsiteResearchAgent


async def test_company_search_simple():
    load_dotenv(".env.local", override=True)
    agent = WebsiteResearchAgent()
    result = await agent.research_website(
        website_url="https://www.silvi.com",
        research_task="""
帮我调研 Silvi Materials ，我关注他们是否拥有车队管理系统，要不要从车辆上采集数据，目标是
他们有没有在车辆上使用网关、边缘计算设备的需求
""",
    )
    assert result is not None


async def test_website_research():
    load_dotenv(".env.local", override=True)
    agent = WebsiteResearchAgent()

    result = await agent.research_website(
        website_url="www.mastdigital.co.uk",
        research_task=f"""
作为映翰通（InHand Networks）SDR，请深度调研以下公司的 reseller 合作潜力。

## 背景介绍
{inhand_business_card}

我们正在寻找合适的 reseller 合作伙伴来扩展全球市场覆盖。

## 核心调研问题
1. 该公司是否具备成为我们 reseller 的基本条件？
2. 他们的业务模式与我们的产品线匹配度如何？
3. 合作的可行性和优先级如何评估？

## 详细调研维度

### 1. 公司基本画像
- **基本信息**：公司全名、成立时间、注册地址、员工规模、年营收
- **联系方式**：官网、总部电话、销售邮箱、主要办公地址
- **公司性质**：是否为本土公司、公司类型（分销商/集成商/解决方案提供商）
- **业务模式**：主要盈利模式、销售渠道、服务范围

### 2. 产品和服务分析
- **现有产品线**：主要销售的产品类别和品牌
- **技术领域**：涉及的技术方向（工业自动化、物联网、网络通信等）
- **硬件产品**：是否已经销售路由器、网关、边缘计算等相关硬件
- **现有供应商**：目前合作的主要硬件供应商品牌
- **产品定位**：产品价格区间、目标客户群体

### 3. 市场定位和客户群
- **目标行业**：主要服务的行业领域
- **客户类型**：终端客户 vs 渠道客户比例
- **市场覆盖**：地理覆盖范围、销售网络
- **竞争地位**：在当地市场的地位和声誉

### 4. 合作伙伴生态
- **现有合作伙伴**：官方合作伙伴列表
- **竞品分析**：是否代理我们的竞争对手产品（如 Cisco、Sierra Wireless、Advantech 等）
- **合作层级**：是否为品牌的核心/战略合作伙伴

### 5. 财务和运营能力
- **公司规模**：营收规模、员工数量、资金实力
- **技术能力**：是否有技术支持团队、工程师配置
- **销售能力**：销售团队规模、销售网络覆盖
- **服务能力**：售前售后服务能力

### 6. 风险评估要素
- **竞品冲突**：与现有供应商的排他性协议
- **技术匹配**：技术栈和客户需求与我们产品的匹配度
- **市场重叠**：目标客户群与我们的重叠程度

## 关键评估标准

### 高优先级指标（必须满足）
- ✅ 有相关硬件产品销售经验
- ✅ 目标客户群与我们产品应用场景匹配
- ✅ 具备基本的技术支持能力
- ✅ 在当地市场有一定知名度和客户基础

### 风险警示（需要特别关注）
- ⚠️ 已经是竞品的核心合作伙伴
- ⚠️ 主要客户群与我们产品应用场景不匹配
- ⚠️ 公司规模过小或财务状况不稳定
- ⚠️ 缺乏技术支持能力

## 输出要求

最后的步骤直接回复，不要输出任何其他内容。
请按以下结构提供详细的调研报告：

### 1. 执行摘要
- 该公司作为 reseller 的整体评级：🔴 低优先级 / 🟡 中优先级 / 🟢 高优先级
- 3-5个关键发现点
- 一句话结论和建议

### 2. 公司概况
- 基本信息和联系方式
- 业务模式和产品线
- 市场地位和客户群体
- 团队规模和能力

### 3. 产品匹配度分析
- 现有硬件产品线及供应商
- 与映翰通产品的匹配程度分析
- 潜在替代或补充机会
- 技术栈兼容性评估

### 4. 合作可行性评估
- **优势因素**：支持合作的积极因素
- **风险因素**：可能的障碍和挑战
- **机会分析**：市场机会和业务增长点
- **威胁分析**：竞争威胁和市场风险

### 5. SDR 行动建议
- **接触策略**：推荐的接触方式和时机
- **关键人员**：建议联系的决策人和影响者
- **价值主张**：针对该公司的核心卖点
- **后续步骤**：具体的行动计划

### 6. 资源清单
- 重要信息源链接
- 关键联系人信息

## 注意事项
- 所有事实必须有可靠来源引用 [[序号]](链接)
- 如果某些信息无法获取，请明确标注
- 重点关注与我们业务相关的信息
- 评估要客观，避免过度乐观或悲观
- 提供具体可执行的建议

## 信息收集要求
- 所有信息必须来源于公开、可验证的渠道
- 提供具体的信息源链接和引用
- 区分确认信息和推测信息
- 明确标注无法获取的信息项目
- 关注时效性，优先获取最新信息
- 收集具体数据，避免模糊描述

请开始调研并生成详细报告。
""",  # noqa: E501
    )
    assert result is not None
