import uuid

from loguru import logger

from server.contacts.discovery.contacts_discovery_processor import DataProcessor
from server.task.task_callback_handler import task_callback


async def test_contacts_agent():
    agent = DataProcessor(task_id=str(uuid.uuid4()))

    with task_callback(task_id="test_contacts_agent"):
        # with logging_callback():
        # with task_callback(task_id="test_contacts_agent"):
        # result = await agent.ainvoke(
        #     input={
        #         "account_id": "3091799000303764040",
        #         "current_user": {"name": "张三", "email": "<EMAIL>"},
        #         "custom_prompts": {
        #             "prompt": "直接放开所有要求，搜这家公司的人。你只需要返回任意一个联系人即可",
        #         },
        #     }
        # )
        from agent.tools.apollo import apollo
        from server.contacts.discovery.contacts_agent_processor import ContactsAgent
        from zoho.accounts_api import fetch_accounts_info_by_account_id

        try:
            zoho_account = await fetch_accounts_info_by_account_id("3091799000009067187")
            apollo_organzation_id = await apollo.search_organization_id(
                f"{zoho_account.get('name')} {zoho_account.get('website')}"
            )
            result = await ContactsAgent().get_contacts(
                # user_query="帮我找出目标企业下最合适的联系人，要求联系人是New Zealand地区的",
                user_query="The engineering leader in charge of telematics.",
                organization_id=apollo_organzation_id,
            )
        except Exception as e:
            logger.opt(exception=e).error("获取联系人失败 {}", e, exc_info=True)
            return

    if isinstance(result, Exception):
        print(result)
        return
    for person in result:
        print(f"name: {person.get('name')}, linkedin_url: {person.get('linkedin_url')}")
        print(f"priority: {person.get('priority')}")
        print(f"title: {person.get('title')}")
        print(f"thinking: {person.get('thinking')}")
        print("-" * 80)
