from typing import TypedDict

from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field

from agent.account.models import EnrichedAccountInfo, LookalikeCompanyInfo
from agent.tools.apollo import apollo
from utils.extractors import extract_structure_response, extract_summary, extract_title


async def test_extract_structure_response():
    class User(BaseModel):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        """
<PERSON> is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
        User,
    )
    assert response is not None
    assert response.name == "<PERSON>"
    assert response.age == 30
    assert response.email == "<EMAIL>"
    assert response.phone == "************"


async def test_extract_structure_response_with_gemini():
    class User(BaseModel):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        """
<PERSON> is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
        User,
        model="gemini-2.5-flash-instant",
    )
    assert response is not None
    assert response.name == "<PERSON>"
    assert response.age == 30
    assert response.email == "<EMAIL>"
    assert response.phone == "************"


async def test_extract_structure_response_with_message():
    class User(BaseModel):
        name: str = Field(description="The name of the user")
        age: int
        email: str
        phone: str
        domain: str = Field(description="The domain of the user, extract from the email")

    response = await extract_structure_response(
        HumanMessage(
            content="张三 is 30 years old and his <NAME_EMAIL>. His phone number is ************. "
        ),
        User,
        model="gemini-2.5-flash-instant",
    )
    assert response is not None
    assert response.name == "张三"
    assert response.domain == "example.com"


async def test_extract_structure_response_with_typed_dict():
    class User(TypedDict):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        """
John Doe is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
        User,
    )

    assert response is not None
    assert response["name"] == "John Doe"


async def test_extract_title():
    title = await extract_title(
        """
John Doe is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
    )
    assert title is not None
    print(title)


async def test_extract_summary():
    summary = await extract_summary(
        """
John Doe is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
    )
    assert summary is not None
    print(summary)


async def test_extract_account_type():
    # account_type: "MSP"
    context = await apollo.get_organization_by_id("54a11e0569702da10f194c01")

    result: LookalikeCompanyInfo = await extract_structure_response(
        context, LookalikeCompanyInfo, model="gpt-5-mini-minimal"
    )
    assert result.account_type == "MSP"

    result = await extract_structure_response(context, LookalikeCompanyInfo)
    assert result.account_type != "MSP"  # wrong result


async def test_extract_address_state():
    # location: "Sydney, NSW 2000, AU"
    context = await apollo.get_organization_by_id("609b6df6e1d27c008d082deb")

    result: EnrichedAccountInfo = await extract_structure_response(
        context, EnrichedAccountInfo, model="gpt-5-mini-minimal"
    )
    assert result.territory == "Australia"
    assert result.address_state == "NSW"
    assert result.billing_city == "Sydney"
    assert result.billing_code == "2000"

    # result = await extract_structure_response(context, EnrichedAccountInfo)
    # assert result.address_state != "NSW"  # wrong result


async def test_extract_industry():
    # industry: "Mobility"
    context = await apollo.get_organization_by_id("5f8ed5f1e4537700be81c048")

    result: EnrichedAccountInfo = await extract_structure_response(
        context, EnrichedAccountInfo, model="gpt-5-mini-minimal"
    )
    assert result.industry == "Mobility"
    assert result.secondary_industry == ["ICT"]

    result = await extract_structure_response(context, EnrichedAccountInfo)
    assert result.industry != "Mobility"  # wrong result
