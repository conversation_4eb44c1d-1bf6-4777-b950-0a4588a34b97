import pytest

from agent.search_people import agentic_search_people, get_person_by_email
from agent.tools.linkedin import rapidapi
from utils.logging_callback_handler import logging_callback


async def test_search_people_api():
    result = await rapidapi.search_people(keywords="<PERSON> Aureyre", title="R&D CTO Office")
    print(result)
    data = result.get("data")
    assert data is not None
    assert result.get("error") is None
    assert len(data) > 0
    assert data[0].get("fullName") is not None
    assert data[0].get("headline") is not None
    assert data[0].get("summary") is not None
    assert data[0].get("profilePicture") is not None
    assert data[0].get("location") is not None
    assert data[0].get("profileURL") is not None


async def test_agentic_search_people():
    with logging_callback():
        result = await agentic_search_people("<PERSON> Aureyre @ Schindler")
        print(result)
        assert result is not None
        assert result.profile_url == "https://www.linkedin.com/in/laurent-aureyre"


async def test_agentic_search_people_by_email():
    with logging_callback():
        result = await agentic_search_people("<PERSON>, <EMAIL>, product manager")
        print(result)
        assert result is not None
        assert result.profile_url == "http://www.linkedin.com/in/alexander-tobiasen-1a2a633"


async def test_agentic_search_people_multi_results():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("Harley")
            print(result)


async def test_agentic_search_people_no_results():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("Someone don't exist")
            print(result)


async def test_agentc_search_people_wrong_query():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("Laurent Aureyre @ InHand Networks")
            print(result)


async def test_get_person_by_email():
    """Test the new get_person_by_email tool"""
    result = await get_person_by_email.coroutine("<EMAIL>")
    print("get_person_by_email result:", result)
    # The result should be either a YAML string or an error message
    assert isinstance(result, str)
    assert len(result) > 0
