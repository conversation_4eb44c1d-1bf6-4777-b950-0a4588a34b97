from unittest.mock import patch

import pytest

from utils import Cache
from utils.cache import skip_cache


@pytest.mark.env("CACHE_ENABLED=False")
async def test_cache_disabled_when_cache_enabled_false():
    """Test that cache is disabled when CACHE_ENABLED=false"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=false, function should be called every time
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3

    # function should be called 3 times since caching is disabled
    assert called_count == 3


@patch.dict("os.environ", {}, clear=True)
async def test_cache_with_custom_key():
    """Test cache with custom key generation function"""
    cache = Cache()
    called_count = 0

    def custom_key_fn(*args, **kwargs):
        # custom key that ignores second argument
        return f"custom_{args[0]}"

    @cache.memoize(key=custom_key_fn)
    def custom_key_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # these should use the same cache key since custom_key_fn only uses first arg
    assert custom_key_func(1, 2) == 3
    assert custom_key_func(1, 999) == 3  # should return cached result (3)

    # this should create a new cache entry
    assert custom_key_func(2, 2) == 4

    assert custom_key_func.__cache_key__(1, 2) == "custom_1"

    # function should only be called twice
    assert called_count == 2


@patch.dict("os.environ", {"CACHE_ENABLED": "true"})
async def test_cache_enabled_when_cache_enabled_true():
    """Test that cache works normally when CACHE_ENABLED=true"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=true, caching should work normally
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


@patch.dict("os.environ", {"CACHE_ENABLED": "true"})
async def test_skip_cache_context_manager_sync():
    """Test that skip_cache context manager forces recomputation for sync functions."""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def add(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # initial call caches
    assert add(1, 2) == 3
    # cached result
    assert add(1, 2) == 3
    assert called_count == 1

    # skip cache forces recompute
    with skip_cache():
        assert add(1, 2) == 3
    assert called_count == 2

    # after context, cache works again (should not increment)
    assert add(1, 2) == 3
    assert called_count == 2


@patch.dict("os.environ", {"CACHE_ENABLED": "true"})
async def test_skip_cache_context_manager_async():
    """Test that skip_cache context manager forces recomputation for async functions."""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    async def add_async(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # initial call caches
    assert await add_async(1, 2) == 3
    # cached result
    assert await add_async(1, 2) == 3
    assert called_count == 1

    # skip cache forces recompute
    with skip_cache():
        assert await add_async(1, 2) == 3
    assert called_count == 2

    # after context, cache works again
    assert await add_async(1, 2) == 3
    assert called_count == 2

    # skip cache forces recompute
    assert await add_async(1, 2, _skip_cache=True) == 3
    assert called_count == 3


@patch.dict("os.environ", {}, clear=True)
async def test_cache_enabled_when_no_cache_enabled_env():
    """Test that cache works normally when CACHE_ENABLED is not set"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def no_env_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED is not set, caching should work normally (default enabled)
    assert no_env_func(1, 2) == 3
    assert no_env_func(1, 2) == 3
    assert no_env_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


@patch.dict("os.environ", {"CACHE_ENABLED": "true"})
async def test_async_cache_enabled_when_cache_enabled_true():
    """Test that async cache works normally when CACHE_ENABLED=true"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    async def async_enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=true, caching should work normally
    assert await async_enabled_func(1, 2) == 3
    assert await async_enabled_func(1, 2) == 3
    assert await async_enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


@patch.dict("os.environ", {}, clear=True)
async def test_async_cache_with_custom_key():
    """Test async cache with custom key generation function"""
    cache = Cache()
    called_count = 0

    def custom_key_fn(*args, **kwargs):
        # custom key that ignores second argument
        return f"async_custom_{args[0]}"

    @cache.memoize(key=custom_key_fn)
    async def async_custom_key_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # these should use the same cache key since custom_key_fn only uses first arg
    assert await async_custom_key_func(1, 2) == 3
    assert await async_custom_key_func(1, 999) == 3  # should return cached result (3)

    # this should create a new cache entry
    assert await async_custom_key_func(2, 2) == 4

    assert async_custom_key_func.__cache_key__(1, 2) == "async_custom_1"

    # function should only be called twice
    assert called_count == 2


@patch.dict("os.environ", {"CACHE_ENABLED": "false"})
async def test_async_cache_disabled_when_cache_enabled_false():
    """Test that async cache is disabled when CACHE_ENABLED=false"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    async def async_disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=false, function should be called every time
    assert await async_disabled_func(1, 2) == 3
    assert await async_disabled_func(1, 2) == 3
    assert await async_disabled_func(1, 2) == 3

    # function should be called 3 times since caching is disabled
    assert called_count == 3


@patch.dict("os.environ", {"CACHE_ENABLED": "0"})
async def test_cache_disabled_when_cache_enabled_zero():
    """Test that cache is disabled when CACHE_ENABLED=0 (falsy value)"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=0, function should be called every time
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3

    # function should be called 3 times since caching is disabled
    assert called_count == 3


@patch.dict("os.environ", {"CACHE_ENABLED": ""})
async def test_cache_disabled_when_cache_enabled_empty_string():
    """Test that cache is disabled when CACHE_ENABLED='' (empty string, falsy value)"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED='', function should be called every time
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3
    assert disabled_func(1, 2) == 3

    # function should be called 3 times since caching is disabled
    assert called_count == 3


@patch.dict("os.environ", {"CACHE_ENABLED": "1"})
async def test_cache_enabled_when_cache_enabled_one():
    """Test that cache works when CACHE_ENABLED=1 (truthy value)"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=1, caching should work normally
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


@patch.dict("os.environ", {"CACHE_ENABLED": "yes"})
async def test_cache_enabled_when_cache_enabled_yes():
    """Test that cache works when CACHE_ENABLED=yes (truthy value)"""
    cache = Cache()
    called_count = 0

    @cache.memoize()
    def enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when CACHE_ENABLED=yes, caching should work normally
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3
    assert enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


def test_default_cache_enabled_true():
    """Test that cache is enabled by default when default_cache_enabled=True"""
    cache = Cache(default_cache_enabled=True)
    called_count = 0

    @cache.memoize()
    def default_enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when default_cache_enabled=True, caching should work normally
    assert default_enabled_func(1, 2) == 3
    assert default_enabled_func(1, 2) == 3
    assert default_enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


def test_default_cache_enabled_false():
    """Test that cache is disabled by default when default_cache_enabled=False"""
    cache = Cache(default_enabled=False)
    called_count = 0

    @cache.memoize()
    def default_disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when default_cache_enabled=False, function should be called every time
    assert default_disabled_func(1, 2) == 3
    assert default_disabled_func(1, 2) == 3
    assert default_disabled_func(1, 2) == 3

    # function should be called three times (no caching)
    assert called_count == 3


def test_enabled_override_default():
    """Test that enabled parameter can override default_cache_enabled"""
    cache = Cache(default_enabled=True)
    called_count = 0

    @cache.memoize(default_enabled=False)
    def override_enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # even though default_cache_enabled=False, enabled=True should enable caching
    assert override_enabled_func(1, 2) == 3
    assert override_enabled_func(1, 2) == 3
    assert override_enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 3


@pytest.mark.asyncio
async def test_async_default_cache_enabled_true():
    """Test that async cache is enabled by default when default_cache_enabled=True"""
    cache = Cache(default_enabled=True)
    called_count = 0

    @cache.memoize()
    async def default_enabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when default_cache_enabled=True, caching should work normally
    assert await default_enabled_func(1, 2) == 3
    assert await default_enabled_func(1, 2) == 3
    assert await default_enabled_func(1, 2) == 3

    # function should only be called once due to caching
    assert called_count == 1


@pytest.mark.asyncio
async def test_async_default_cache_enabled_false():
    """Test that async cache is disabled by default when default_cache_enabled=False"""
    cache = Cache(default_enabled=False)
    called_count = 0

    @cache.memoize()
    async def default_disabled_func(a, b):
        nonlocal called_count
        called_count += 1
        return a + b

    # when default_cache_enabled=False, function should be called every time
    assert await default_disabled_func(1, 2) == 3
    assert await default_disabled_func(1, 2) == 3
    assert await default_disabled_func(1, 2) == 3

    # function should be called three times (no caching)
    assert called_count == 3
