from pprint import pprint

import pytest
import yaml

from agent.tools.apollo import apollo, iter_apollo_page_result
from utils import logging_callback


async def test_search_people():
    query = "<PERSON> | R&D CTO Office @ Schindler US"
    result = await apollo.search_people(
        person_titles=[
            "Director of Telematics",
            "Connected Support Director",
        ],
        organization_ids=["615dc78121eb73000160beee"],
    )
    pprint(result)
    assert "error" not in result


async def test_search_organization():
    kwargs = {
        "q_organization_keyword_tags": [
            "wind turbine manufacturer",
            "wind turbine OEM",
            "wind blade manufacturer",
            "wind tower manufacturer",
            "wind turbine components",
            "nacelle manufacturer",
            "wind energy developer",
            "wind power developer",
            "wind farm developer",
            "wind project developer",
            "wind farm operator",
            "wind power company",
            "wind energy company",
            "independent power producer",
            "IPP",
            "renewable energy IPP",
            "wind O&M",
            "wind operations and maintenance",
            "wind energy EPC",
            "wind farm EPC",
            "onshore wind",
        ],
        "organization_locations": ["South America"],
        "organization_num_employees_ranges": [],
    }
    result = await apollo.search_organization(
        q_organization_keyword_tags=[
            "wind turbine manufacturer",
            "wind turbine OEM",
            "wind blade manufacturer",
            "wind tower manufacturer",
            "wind turbine components",
            "nacelle manufacturer",
            "wind energy developer",
            "wind power developer",
            "wind farm developer",
            "wind project developer",
            "wind farm operator",
            "wind power company",
            "wind energy company",
            "independent power producer",
            "IPP",
            "renewable energy IPP",
            "wind O&M",
            "wind operations and maintenance",
        ],
        organization_locations=["South America"],
    )
    pprint(result)
    assert result.error is None

    # for company in result.data:
    #     company_info = await apollo.get_organization_by_id(company.get("id"))
    #     print(company_info.get("name") + ":" + company_info.get("website_url"))
    #     pprint(company_info.get("keywords"))


async def test_get_organization_by_id():
    result = await apollo.get_organization_by_id("60b2d0545e799d0001881d19")
    pprint(result)
    assert "error" not in result

    print(yaml.dump(result, indent=2))


async def test_get_complete_organization_info_by_id():
    result = await apollo.get_complete_organization_info(id="60b2d0545e799d0001881d19")
    pprint(result)
    assert "error" not in result

    print(yaml.dump(result, indent=2))


async def test_get_complete_organization_info_wrong_name():
    # should raise error when organization info is not found
    with pytest.raises(Exception):
        result = await apollo.get_complete_organization_info(name="Smart Tek LLC", website_url="smartek-its.com")
        pprint(result)


async def test_get_complete_organization_info_by_name():
    result = await apollo.get_complete_organization_info(name="Kaishan Australia", location="Australia")
    pprint(result)
    assert "error" not in result
    assert result.get("website_url") == "http://www.kaishan.com.au"


async def test_get_complete_organization_info_by_name2():
    result = await apollo.get_complete_organization_info(name="Champion Compressors")
    pprint(result)
    assert "error" not in result
    assert result.get("website_url") == "http://www.championcompressors.com.au"


async def test_get_complete_organization_info_wrong_location():
    result = await apollo.get_complete_organization_info(
        name="Vestas Chile Turbinas Eolicas Ltda", website_url="https://www.vestas.com/en", location="Chile"
    )
    pprint(result)
    assert "error" not in result
    assert "www.vestas.com" in result.get("website_url")


async def test_get_complete_organization_info_by_zoho_account_id():
    result = await apollo.get_complete_organization_info(name="CTS Mobility", location="Texas")
    pprint(result)
    assert "error" not in result


@pytest.mark.env("APOLLO_CACHE_ENABLED=False")
async def test_search_organization_id():
    from zoho.accounts_api import fetch_accounts_info_by_account_id

    with logging_callback():
        zoho_account = await fetch_accounts_info_by_account_id("3091799000311732007")
        result = await apollo.search_organization_id(
            f"{zoho_account['name']}, {zoho_account.get('location', '')}, {zoho_account.get('website', '')}"
        )  # noqa: E501
        pprint(result)
        assert "error" not in result


async def test_enrich_person():
    # test people enrichment with basic information
    result = await apollo.enrich_person(
        first_name="Tim", last_name="Zheng", organization_name="Apollo", domain="apollo.io"
    )
    pprint(result)
    assert "error" not in result


async def test_enrich_person_with_email():
    # test people enrichment with email
    result = await apollo.enrich_person(email="<EMAIL>")
    pprint(result)
    assert "error" not in result
    assert "product manager" in result.get("title", "").lower()


async def test_enrich_person_by_id():
    result = await apollo.enrich_person(id="557149bc7369642ba5de4000", reveal_phone_number=True)
    pprint(result)
    assert "error" not in result
    assert "phone_numbers" in result
    assert "email_addresses" in result


async def test_add_to_my_prospects():
    result = await apollo.add_to_my_prospects(apollo_person_id="66664eb8cf8c030001f014da")
    pprint(result.get("phone_numbers"))
    assert "error" not in result


async def test_get_company_lookalikes():
    result = await apollo.get_company_lookalikes(id="5f487532714b8b00013bf7e3")
    pprint(result)
    assert result.error is None


async def test_get_all_company_lookalikes():
    async for companies in iter_apollo_page_result(
        apollo.get_company_lookalikes,
        id="5f487532714b8b00013bf7e3",
        organization_locations=["Oklahoma", "Texas", "Louisiana", "Arkansas"],
    ):
        for company in companies:
            pprint(company)
