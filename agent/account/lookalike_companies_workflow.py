import json
from typing import List, NotRequired, Optional, TypedDict

from langchain_core.callbacks import dispatch_custom_event
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from agent.account.icp_analysis import icp_analysis
from agent.account.search_lookalike_companies import search_lookalike_companies
from agent.tools.apollo import apollo


class LookalikeCompaniesState(TypedDict):
    """找相似公司的状态数据结构"""

    # 输入信息
    user_query: str  # 用户查询需求
    zoho_account_info: dict  # zoho crm上的公司信息（模板公司）
    excludes: List[str]  # 排除的公司列表

    # 业务数据
    apollo_company_info: NotRequired[dict]  # 参考公司的Apollo详细信息（用于 ICP 分析）
    ideal_customer_profile: NotRequired[str]  # 理想客户画像分析结果
    lookalike_companies: NotRequired[list[dict]]  # lookalike公司列表
    title: NotRequired[str]  # 任务标题


class LookalikeCompaniesStateGraph:
    """找相似公司的状态图"""

    def __init__(self):
        self.graph = self._create_graph()

    def _create_graph(self) -> CompiledStateGraph:
        """创建状态图"""
        workflow = StateGraph(LookalikeCompaniesState)

        # 添加业务节点
        workflow.add_node("get_apollo_company_info", self._get_apollo_company_info_node)
        workflow.add_node("analyze_ideal_customer_profile", self._analyze_ideal_customer_profile_node)
        workflow.add_node("search_lookalike_companies", self._search_lookalike_companies_node)

        # 设置入口点
        workflow.set_entry_point("get_apollo_company_info")

        # 设置条件边
        workflow.add_conditional_edges(
            "get_apollo_company_info",
            self._should_skip_analyze_icp,
            {"skip": "search_lookalike_companies", "continue": "analyze_ideal_customer_profile"},
        )
        workflow.add_edge("analyze_ideal_customer_profile", "search_lookalike_companies")
        workflow.add_edge("search_lookalike_companies", END)

        return workflow.compile()

    def _should_skip_analyze_icp(self, state: LookalikeCompaniesState) -> str:
        """条件函数：决定是否跳过icp分析"""
        if state.get("ideal_customer_profile"):
            return "skip"
        else:
            return "continue"

    async def _get_apollo_company_info_node(self, state: LookalikeCompaniesState) -> LookalikeCompaniesState:
        """获取 apollo 公司信息节点"""
        if state.get("apollo_company_info"):
            return state

        zoho_account_info = state["zoho_account_info"]
        name = zoho_account_info.get("name", "")
        website = zoho_account_info.get("website", "")

        apollo_company_info = {"name": name, "website": website}
        try:
            # fetch complete organization info from apollo
            apollo_info = await apollo.get_complete_organization_info(
                query=json.dumps(zoho_account_info),
            )
            if apollo_info.get("id"):
                apollo_company_info = apollo_info
            else:
                logger.error(f"cannot found the company in apollo for {name}: {apollo_info.get('error', '')}")
        except Exception as e:
            logger.error(f"Error fetching complete organization info from apollo: {str(e)}")

        state["apollo_company_info"] = apollo_company_info
        return state

    async def _analyze_ideal_customer_profile_node(self, state: LookalikeCompaniesState) -> LookalikeCompaniesState:
        """获取理想客户画像分析节点"""
        apollo_company_info = state["apollo_company_info"]
        name = apollo_company_info.get("name", "")

        dispatch_custom_event(
            "thinking",
            f"I will first analyze the reference company {name} to understand the Ideal Customer Profile (ICP) based on its business model, products, and characteristics.",  # noqa: E501
        )

        # 分析理想客户画像
        ideal_customer_profile = await icp_analysis(
            user_query=state["user_query"],
            company_apollo_id=apollo_company_info.get("id"),
            lookalike_company=json.dumps(apollo_company_info),
        )
        state["ideal_customer_profile"] = ideal_customer_profile
        return state

    async def _search_lookalike_companies_node(self, state: LookalikeCompaniesState) -> LookalikeCompaniesState:
        """根据理想客户画像搜索lookalike公司节点"""
        apollo_company_info = state["apollo_company_info"]
        company_name = apollo_company_info.get("name", "")
        dispatch_custom_event(
            "thinking",
            f"Based on the ICP analysis of {company_name}, I will search for companies that match the ideal customer profile. Next, I will gradually achieve this goal.",  # noqa: E501
        )

        result = await search_lookalike_companies(
            apollo_company_info=apollo_company_info,
            ideal_customer_profile=state["ideal_customer_profile"],
            user_query=state["user_query"],
            excludes=state["excludes"],
        )

        state["title"] = result.get("title", state["user_query"])
        state["lookalike_companies"] = result.get("lookalike_companies", [])
        return state

    async def execute(self, initial_state: LookalikeCompaniesState) -> dict:
        """执行工作流"""
        logger.debug("Start lookalike companies workflow...")
        try:
            final_state: LookalikeCompaniesState = await self.graph.ainvoke(initial_state)
            logger.debug("Lookalike companies workflow completed")

            return {
                "title": final_state["title"],
                "data": final_state["lookalike_companies"],
                "ideal_customer_profile": final_state["ideal_customer_profile"],
                "apollo_company_info": final_state["apollo_company_info"],
            }
        except Exception as e:
            logger.error(f"Lookalike companies workflow failed: {str(e)}")
            raise Exception(f"execute lookalike companies workflow failed: {str(e)}")


async def execute_lookalike_companies_workflow(
    user_query: str,
    zoho_account_info: dict,
    excludes: list[str],
    ideal_customer_profile: Optional[str] = None,
    apollo_company_info: Optional[dict] = None,
) -> dict:
    """
    执行找相似公司工作流

    Args:
        user_query: user query, customer prompt
        zoho_account_info: zoho account info
        excludes: list of company names to exclude
        ideal_customer_profile: ideal customer profile
        apollo_company_info: apollo company info

    Returns:
        dict: 找相似公司结果
        {
            "title": "...", # 标题
            "data": [], # lookalike_companies
            "ideal_customer_profile": "...", # 理想客户画像
            "apollo_company_info": {...} # 参考公司apollo详细信息
        }
    """
    name = zoho_account_info.get("name", "")
    website = zoho_account_info.get("website", "")
    if not name and not website:
        raise Exception("Missing company name or website")

    # 排除当前公司
    if len(excludes) == 0 or name not in excludes:
        excludes.append(name)

    initial_state = LookalikeCompaniesState(
        user_query=user_query,
        zoho_account_info=zoho_account_info,
        excludes=excludes,
        ideal_customer_profile=ideal_customer_profile,
        apollo_company_info=apollo_company_info,
    )
    lookalike_companies_state_graph = LookalikeCompaniesStateGraph()
    return await lookalike_companies_state_graph.execute(initial_state)
