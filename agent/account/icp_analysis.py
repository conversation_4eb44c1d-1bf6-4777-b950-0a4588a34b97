from pathlib import Path

from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.apollo import get_complete_organization_info
from common.prompts import inhand_business_card
from utils import Cache
from utils import safe_dispatch_custom_event as dispatch_custom_event
from utils.models import init_model

cache = Cache(
    directory=Path(__file__).parent / ".cache/icp_analysis",
    timeout=7 * 24 * 60 * 60,
    cache_enabled_env_var="ICP_CACHE_ENABLED",
)


@cache.memoize(ignore=["lookalike_company"])
async def icp_analysis(
    user_query: str | None,
    company_apollo_id: str = None,
    lookalike_company: str = None,
) -> str:
    """
    分析理想客户画像(ICP)，基于参考公司的特征来定义目标客户群体的共同特点

    Args:
        user_query: 用户查询需求
        company_apollo_id: 参考公司的Apollo ID（可选）
        lookalike_company: 参考的相似公司描述信息（可选）
    Returns:
        dict: ICP分析结果
        {
            "name": "公司名称",
            "research_result": "ICP分析结果",
            "error": "错误信息",
        }
    """
    try:
        # 校验参数
        user_query = user_query or "find companies lookalike this one"

        logger.debug(f"开始分析理想客户画像, user_query: {user_query}, company_apollo_id: {company_apollo_id}")
        dispatch_custom_event("thinking", "Starting ideal customer profile analysis...")

        llm = init_model(
            model="gpt-5-low",
            text={"verbosity": "low"},
            max_tokens=4096,
        )

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
            get_complete_organization_info,
        ]
        llm.bind_tools(research_tools, tool_choice="any")

        # 构建提示词和输入
        prompt_path = Path(__file__).parent / "prompts" / "icp_analysis.md"
        system_prompt = prompt_path.read_text(encoding="utf-8")

        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")]).partial(
            inhand_business_card=inhand_business_card
        )
        agent = create_react_agent(
            model=llm,
            tools=research_tools,
            prompt=prompt,
            name="analyze_ideal_customer_profile_agent",
        )

        input_message_parts = [f"User Query Requirements:\n```\n{user_query}\n```"]

        if lookalike_company:
            input_message_parts.append(f"Reference Company Information:\n```\n{lookalike_company}\n```")
        else:
            input_message_parts.append("No reference company provided.")

        input_message_parts.append(
            "分析用户需求，理解用户想要调研什么样的公司，根据用户的需求对参考的模板公司进行分析，并生成理想客户画像。"
        )

        input_message = "\n\n".join(input_message_parts)

        result = await agent.ainvoke(input={"messages": [("human", input_message)]})

        # 提取最终结果
        message = result["messages"][-1]
        if isinstance(message, AIMessage) and message.content:
            research_result = message.text()
            dispatch_custom_event("thinking", "ideal customer profile analysis completed.")

            return research_result
        else:
            raise Exception("No valid research result")

    except Exception as e:
        error_msg = f"failed to analyze ideal customer profile: {str(e)}"
        raise Exception(error_msg)
