import asyncio
import json

from langfuse import get_client
from loguru import logger

from agent.account.models import EnrichedAccountInfo
from agent.tools.apollo import apollo
from utils.extractors import extract_structure_response
from zoho.accounts_api import fetch_accounts_info_by_account_id


async def get_enriched_account_info(account_id: str, username: str = None, email: str = None) -> dict:
    """
    获取丰富后的账户信息

    Args:
        account_id: Zoho账户ID
        username: 用户名
        email: 用户邮箱

    Returns:
        dict: 丰富后的账户信息（EnrichedAccountInfo）
    """
    try:
        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
        if not zoho_account_info:
            raise Exception(f"Account {account_id} not found")

        # 验证基本信息
        company_name = zoho_account_info.get("name")
        contact_location = zoho_account_info.get("territory")
        organization_website = zoho_account_info.get("website")

        if not company_name and not organization_website and not contact_location:
            raise Exception("Missing company name or website or territory")

        langfuse_client = get_client()
        with langfuse_client.start_as_current_span(
            name="enriched_account_info",
            input=f"Account Name: {company_name} , Account ID :{account_id}",
        ) as langfuse_trace_span:
            langfuse_client.update_current_trace(
                user_id=email,
                metadata={
                    "account_id": account_id,
                    "user_info": {
                        "username": username,
                        "email": email,
                    },
                },
            )

            # 在 Apollo 上获取公司完整信息
            apollo_company_info = await apollo.get_complete_organization_info(
                name=company_name,
                location=contact_location,
                website_url=organization_website,
            )

            if "error" in apollo_company_info:
                langfuse_trace_span.update(
                    output={"error": f"Error fetching company info from Apollo: {apollo_company_info['error']}"}
                )
                raise Exception(f"Error fetching company info from Apollo: {apollo_company_info['error']}")

            # 提取结构化信息
            enriched_account_info = await extract_structure_response(
                apollo_company_info, EnrichedAccountInfo, model="gpt-5-mini-minimal"
            )
            langfuse_trace_span.update(output=enriched_account_info)
            return enriched_account_info.model_dump(exclude_none=True)

    except Exception as e:
        logger.error(f"Account enrichment workflow failed: {str(e)}")
        raise e


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    result = asyncio.run(get_enriched_account_info("3091799000288053001"))
    print(json.dumps(result, indent=4, ensure_ascii=False))
