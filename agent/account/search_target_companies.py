from pathlib import Path
from typing import List, Optional, cast

import yaml
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import chain
from langchain_core.tools import tool
from langfuse import get_client
from langgraph.prebuilt import create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.models import SearchCompaniesResult
from agent.tools.apollo import apollo, get_organization_by_id
from agent.tools.final_answer import create_final_answer_tool
from utils import init_model
from utils.extractors import extract_structure_response
from utils.file_handler import load_file


class FinalAnswer(BaseModel):
    ids: list[str] | None = Field(None, description="The ids of the companies")
    error: str | None = Field(None, description="The error message if the search failed, must be in English")


class SearchOrganizationsSchema(BaseModel):
    """搜索组织的参数模型"""

    q_organization_name: Optional[str] = Field(
        default=None,
        description="""Filter search results to include a specific company name or domain name.""",
    )
    q_organization_keyword_tags: Optional[List[str]] = Field(
        default=None,
        description="""Filter search results based on keywords associated with companies.
        For example, you can enter mining as a value to return only companies that have an association with the mining industry.
        Always use English keywords.

        To improve search results, include multiple variants of the same keyword when possible:
        - For "air compressor manufacturer", also include "air compressor", "compressor"
        - For "software development", also include "software", "development", "programming"
        - For "data analytics", also include "data", "analytics", "business intelligence"

        """,  # noqa: E501
    )
    organization_locations: Optional[List[str]] = Field(
        default=None,
        description="""The location of the company headquarters.
        You can search across cities, US states, and countries.""",  # noqa: E501
    )


@tool(args_schema=SearchOrganizationsSchema)
async def search_organizations(
    q_organization_name: str | None = None,
    q_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Args:
        q_organization_name: Filter search results to include a specific company name.
        q_organization_keyword_tags: Filter search results based on keywords associated with companies.
        organization_locations: The location of the company headquarters.
        organization_num_employees_ranges: The number range of employees working for the company.
        include_snippets: Whether to include snippets in the search results, default is False,
            e.g. company keywords, industry, city, state, country, etc.
            Only set this parameter when you really need to know more about the company.

    Returns:
        Search results in YAML format
    """  # noqa: E501

    try:
        result = await apollo.search_organization(
            q_organization_name=q_organization_name,
            q_organization_keyword_tags=q_organization_keyword_tags,
            organization_locations=organization_locations,
            per_page=10,
            include_snippets=False,
        )
    except Exception as e:
        logger.opt(exception=e).error("search organizations error")
        return f"error: {str(e)}"

    if result.error:
        return f"error: {result.error}"
    else:
        logger.info(f"search organizations result: {len(result.data)} organizations found")
        # filter fields in result list
        return f"Total {len(result.data)} organizations found\n\n```yaml\n{yaml.dump(result.data, indent=2)}\n```"


@chain
async def search_companies_info(user_query: str) -> str:
    tools = [search_organizations, create_final_answer_tool(FinalAnswer)]
    llm: BaseChatModel = init_model(model="gemini-2.5-flash", thinking_budget=128).bind_tools(tools, tool_choice="any")

    system_prompt = load_file(Path(__file__).parent / "prompts" / "search_target_companies.md")
    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    agent = create_react_agent(model=llm, tools=tools, prompt=prompt)

    result = await agent.ainvoke({"messages": [("human", user_query)]})
    last_message = result.get("messages")[-1]
    if isinstance(last_message, ToolMessage) and isinstance(last_message.artifact, FinalAnswer):
        answer = cast(FinalAnswer, last_message.artifact)
        if ids := answer.ids:
            organizations = await get_organization_by_id.abatch(ids)
            return organizations
        elif error := answer.error:
            raise RuntimeError(error)

    raise RuntimeError("final answer tool not called")


@chain
async def extract_companies_result(content: str) -> SearchCompaniesResult:
    if "error" in content:
        return SearchCompaniesResult(error="company not found", companies=[])

    return await extract_structure_response(content, SearchCompaniesResult, model="gpt-5-mini-minimal")


async def search_target_companies(user_query: str, user_info: dict | None = None) -> SearchCompaniesResult:
    """
    根据 user_query 搜索公司信息

    Args:
        user_query: 用户查询
        user_info: 用户信息

    Returns:
        SearchCompaniesResult: 搜索公司信息结果
    """
    langfuse = get_client()
    with langfuse.start_as_current_span(
        name="search_target_companies",
        input=user_query,
    ) as span:
        langfuse.update_current_trace(
            user_id=user_info.get("email") if user_info else None,
            metadata={
                "user_info": user_info,
            },
        )
        try:
            agent = search_companies_info | extract_companies_result
            result = await agent.ainvoke(user_query)

            span.update(output=result)
            return result

        except Exception as e:
            logger.error(f"search company info failed: {e}")

            span.update(status_message=str(e), level="ERROR")
            return SearchCompaniesResult(error=str(e), companies=[])
