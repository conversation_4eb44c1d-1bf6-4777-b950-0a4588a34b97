from typing import Annotated, List, Optional, TypedDict

import yaml
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import (
    RunnableConfig,
    RunnableLambda,
    RunnableSerializable,
)
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.prebuilt import InjectedState
from langgraph.types import Command
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.models import CompanySimilarityAnalysis, LookalikeCompanyInfo
from agent.tools.apollo import apollo
from utils import init_model
from utils import safe_dispatch_custom_event as dispatch_custom_event
from utils.extractors import extract_structure_response

# Similarity threshold for filtering companies
SIMILARITY_THRESHOLD = 0.6

similarity_chain: Optional[RunnableSerializable] = None


def get_similarity_chain() -> RunnableSerializable:
    """build and cache a similarity analysis chain"""
    global similarity_chain

    if similarity_chain is None:
        # use llm to analyze similarity
        llm = init_model(
            # model="o4-mini-low",
            # max_tokens=4096,
            model="gpt-5-minimal",
            # text={"verbosity": "low"},
            # model="gemini-2.5-flash",
            # include_thoughts=True,
            # thinking_budget=512,
        ).with_structured_output(CompanySimilarityAnalysis, method="function_calling")

        output_parser = PydanticOutputParser(pydantic_object=CompanySimilarityAnalysis)

        # build analysis prompt
        prompt = ChatPromptTemplate.from_template(
            """
You are a business analyst expert. Analyze the similarity between target company and reference company
based on user requirements.

## Analysis Task:
1. Calculate similarity score (0-1, where 1 is perfect match) based on user requirements
2. Provide a concise one-sentence description of the similarity
3. List specific similarity reasons and key differences
4. Follow the lookalike criteria in `Target Company Information` section to determine the similarity score
5. Use search tool to get more information about the target company if needed
6. The similarity score must be greater than or equal to {similarity_threshold} to be considered similar

## Similarity Criteria:

1. Meet all essential conditions
2. If any exclusion condition is met, it is considered not similar
3. Meet all verification criteria
4. Their main products and business should be consistent, especially for hardware products, the main hardware product form and core functions
    of the target company should be highly similar to the reference company, that is, they have direct comparability in product type and way of achieving core business value.

## Output language

1. Default working language: **English**
2. Use the language specified by user in messages as the working language when explicitly provided
3. All thinking and responses must be in the working language
4. Natural language arguments in tool calls must be in the working language
5. Avoid using pure lists and bullet points format in any language

## Output fields
1. description:
    if similarity_score >= {similarity_threshold}, list all specific similarity reasons
    if similarity_score < {similarity_threshold}, list all specific differences or exclusion reasons
2. company_name: the name of the target company
3. similarity_score: the similarity score between the target company and the reference company

{format_instructions}

Focus on aspects most relevant to the user requirements when calculating similarity.
Use precise decimal values for similarity_score (e.g., 0.85, 0.62, 0.91).

## User Requirements:
```
{user_query}
```

## Reference Company Information:
```
{reference_company}
```

## Target Company Information:

```yaml
{target_company_info}
```
"""  # noqa: E501
        ).partial(
            similarity_threshold=SIMILARITY_THRESHOLD,
            format_instructions=output_parser.get_format_instructions(),
        )
        similarity_chain = prompt | llm

    return similarity_chain


class CompanySimilarityAnalysisResult(TypedDict, total=False):
    passed: bool | None
    company_name: str | None
    similarity_score: float | None
    explanation: str | None
    company_info: dict | None


async def analyze_company_similarity(
    id: Optional[str] = None,
    name: str = "",
    location: str | None = None,
    website_url: str | None = None,
    user_query: str = "",
    reference_company: str = "",
) -> CompanySimilarityAnalysisResult:
    """
    internal method to analyze similarity for a single company

    Args:
        id: The unique Apollo ID of the organization
        name: The name of the organization to search for
        location: The location of the organization (city, state, or country)
        website_url: The website URL of the organization
        user_query: User query containing requirements
        reference_company: Reference company information

    Returns:
        Single company similarity analysis result
    """
    logger.info(
        f"Starting company similarity analysis - ID: {id}, Name: {name}, Location: {location}, Website: {website_url}"
    )

    if not user_query or not reference_company:
        error_msg = "Missing user_query or reference_company"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # fetch complete organization info
    target_company_info = await apollo.get_complete_organization_info(
        id=id,
        name=name,
        location=location,
        website_url=website_url,
    )

    if error_message := target_company_info.get("error"):
        logger.error(f"Analysis failed: {error_message}")
        raise ValueError(f"Error fetching company info: {error_message}")

    yaml_company_info = yaml.dump(target_company_info)

    analysis: CompanySimilarityAnalysis = await get_similarity_chain().ainvoke(
        {
            "user_query": user_query,
            "reference_company": reference_company,
            "target_company_info": yaml_company_info,
        },
        config=RunnableConfig(run_name="analyze_company_similarity_chain"),
    )

    if analysis.error and analysis.error != "null":
        logger.error(f"Analysis failed: {analysis.error}")
        raise RuntimeError(f"Analysis failed: {analysis.error}")

    return CompanySimilarityAnalysisResult(
        passed=analysis.similarity_score >= SIMILARITY_THRESHOLD,
        company_name=analysis.company_name,
        similarity_score=analysis.similarity_score,
        explanation=analysis.explanation,
        company_info=target_company_info,
    )


class CompanyForAnalysis(BaseModel):
    """company info for similarity analysis"""

    name: str = Field(description="Company name, if no apollo id, describe the company in detail in name field")
    id: Optional[str] = Field(
        default=None,
        description="Apollo organization ID, if apollo id is provided, then other fields are not needed",
    )
    location: Optional[str] = Field(default=None, description="Company location")
    website_url: Optional[str] = Field(default=None, description="Company website URL")


class BulkAnalyzeCompanySimilaritySchema(BaseModel):
    companies: List[CompanyForAnalysis] = Field(description="List of companies to analyze")
    user_query: Annotated[str, InjectedState("user_query")] = Field(description="User query containing requirements")
    reference_company: Annotated[str, InjectedState("reference_company")] = Field(
        description="Reference company information"
    )
    lookalike_company_ids: Annotated[list[str], InjectedState("lookalike_company_ids")] = Field(
        description="Lookalike company IDs"
    )
    tool_call_id: Annotated[str, InjectedToolCallId] = Field(
        description="Injected tool call ID for creating ToolMessage"
    )


@tool(args_schema=BulkAnalyzeCompanySimilaritySchema)
async def bulk_analyze_company_similarity(
    companies: List[CompanyForAnalysis],
    user_query: Annotated[str, InjectedState("user_query")],
    reference_company: Annotated[str, InjectedState("reference_company")],
    lookalike_company_ids: Annotated[list[str], InjectedState("lookalike_company_ids")],
    tool_call_id: Annotated[str, InjectedToolCallId],
) -> str | Command:
    """
    Batch analyze company similarity by comparing multiple target companies with reference company
    based on user requirements.

    This tool processes multiple companies in parallel for efficiency.
    The other company fields isn't needed if apollo id is provided.

    Args:
        companies: List of CompanyForAnalysis objects containing company information
        state: Injected SearchCompaniesState for updating lookalike companies
        tool_call_id: Injected tool call ID for creating ToolMessage

    Returns:
        Batch company similarity analysis results in text format
    """
    if not companies:
        return "Error: No companies provided for analysis"

    logger.info(f"Starting batch analysis for {len(companies)} companies")

    if not user_query or not reference_company:
        error_msg = "Missing user_query or reference_company in state"
        logger.error(error_msg)
        return f"Error: {error_msg}"

    new_lookalikes = []
    new_lookalike_ids = []

    async def analyze_one(company: CompanyForAnalysis) -> str:
        try:
            result = await analyze_company_similarity(
                id=company.id,
                name=company.name,
                location=company.location,
                website_url=company.website_url,
                user_query=user_query,
                reference_company=reference_company,
            )

            company_name = result.get("company_name")
            score = result.get("similarity_score")
            explanation = result.get("explanation")

            if result.get("passed", False):
                company_info = result.get("company_info")

                if company_info.get("id") in lookalike_company_ids:
                    logger.debug(f"company {company_name} is already in lookalikes, skip")
                    return f"Company: {company_name}\nStatus: Already in lookalikes"

                structured_data = await extract_structure_response(
                    company_info, LookalikeCompanyInfo, model="gpt-5-mini-minimal"
                )
                zoho_account_info = structured_data.model_dump(exclude_none=True)

                new_lookalikes.append(zoho_account_info)
                new_lookalike_ids.append(company_info.get("id"))

                logger.success(
                    f"found lookalike company: {company_name} {company_info.get('website_url')}, total found: {len(new_lookalikes) + len(lookalike_company_ids)}"  # noqa: E501
                )

                dispatch_custom_event("data", zoho_account_info)

                return (
                    f"Company: {company_name}\n"
                    f"Similarity Score: {score}\n"
                    f"Status: Recommended (score >= {SIMILARITY_THRESHOLD})\n"
                    f"Reason: {explanation or 'No specific reason provided'}\n"
                    f"Keywords: {company_info.get('keywords')}"
                )
            else:
                logger.opt(colors=True).debug(
                    f"<light-black>company {company_name} is filtered, score: {score}, "
                    f"reason: {explanation}</light-black>"
                )

                return (
                    f"Company: {company_name}\n"
                    f"Similarity Score: {score}\n"
                    f"Status: Filtered (score < {SIMILARITY_THRESHOLD})\n"
                    f"Reason: {explanation or 'No specific reason provided'}"
                )

        except Exception as e:
            return f"Company: {company.name}\nError: {str(e)}"

    results = await RunnableLambda(analyze_one).abatch(companies, config=RunnableConfig(max_concurrency=10))

    summary = f"Total {len(results)} companies analyzed, {len(new_lookalikes)} companies selected as similar companies"  # noqa: E501
    content = f"{summary}\n---\n" + "\n---\n".join(results)

    return Command(
        update={
            "lookalike_companies": new_lookalikes,
            "lookalike_company_ids": new_lookalike_ids,
            "messages": [ToolMessage(content=content, tool_call_id=tool_call_id)],
        }
    )
