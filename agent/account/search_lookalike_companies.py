import json
from pathlib import Path
from typing import Annotated, List, Optional

import yaml
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState, create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.analyze_company_similarity import bulk_analyze_company_similarity
from agent.account.models import SearchCompaniesState
from agent.tools.apollo import apollo
from agent.tools.tavily import tavily_search
from utils import init_model


class SearchOrganizationsArgs(BaseModel):
    """Arguments schema for search_organizations tool"""

    q_organization_keyword_tags: Optional[List[str]] = Field(
        default=None, description="Filter search results to include companies that match any of the specified keywords"
    )
    q_anded_organization_keyword_tags: Optional[List[str]] = Field(
        default=None, description="Filter search results to include companies that match all of the specified keywords"
    )
    q_not_organization_keyword_tags: Optional[List[str]] = Field(
        default=None, description="Filter search results to exclude companies that match any of the specified keywords"
    )
    organization_locations: Optional[List[str]] = Field(
        default=None,
        description=(
            "the location of the company headquarters. "
            "make sure to avoid overlapping regions and countries in the list to prevent redundant filtering."
            "e.g. don't pass in 'Europe' and 'Germany' at the same time, because Germany is part of Europe. "
        ),
    )
    organization_num_employees_ranges: Optional[List[str]] = Field(
        default=None,
        description="The number range of employees working for the company. e.g. 1,10; 250,500; 1,1000; 10000,20000",
    )
    page: int = Field(default=1, description="The page number of the search results")
    lookalike_organization_ids: Optional[List[str]] = Field(
        default=None,
        max_length=5,
        description="The IDs of the lookalike organizations, apollo will return companies "
        "that are similar to the lookalike organizations. Maximum length is 5",
    )


@tool(args_schema=SearchOrganizationsArgs)
async def search_organizations(
    q_organization_keyword_tags: list[str] | None = None,
    q_anded_organization_keyword_tags: list[str] | None = None,
    q_not_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
    organization_num_employees_ranges: list[str] | None = None,
    page: int = 1,
    excludes: Annotated[List[str], InjectedState("excludes")] = [],
    lookalike_organization_ids: list[str] | None = None,
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Only use q_anded_organization_keyword_tags, q_not_organization_keyword_tags when you are sure about the keywords.
    You should always use lookalike_organization_ids, except the search result is too few.
    You can add searched lookalike companies to the lookalike_organization_ids to get more lookalike companies.

    Returns:
        Search results in YAML format including pagination info
    """  # noqa: E501

    try:
        logger.info(
            f"Searching organizations, page: {page},"
            f"q_organization_keyword_tags: {q_organization_keyword_tags},"
            f"organization_locations: {organization_locations},"
            f"organization_num_employees_ranges: {organization_num_employees_ranges},"
            f"excludes: {excludes}",
        )
        response = await apollo.search_organization(
            q_organization_keyword_tags=q_organization_keyword_tags,
            q_anded_organization_keyword_tags=q_anded_organization_keyword_tags,
            q_not_organization_keyword_tags=q_not_organization_keyword_tags,
            organization_locations=organization_locations,
            organization_num_employees_ranges=organization_num_employees_ranges,
            exclude_organization_names=excludes,
            lookalike_organization_ids=lookalike_organization_ids,
            include_snippets=True,
            page=page,
        )

        if response.error:
            return f"Error: {response.error}"

        organizations = response.data or []
        pagination_info = response.pagination

        # simplified pagination info
        pagination_summary = f"Page {page}/{pagination_info.total_pages if pagination_info else 1}"
        if pagination_info.total_entries:
            pagination_summary += f" (Total: {pagination_info.total_entries} entries)"
        current_count = len(organizations)
        pagination_summary += f", {current_count} entries returned in current page"

        logger.info(f"Search organizations completed, {pagination_summary}")

        for org in organizations:
            org["keywords"] = ", ".join(org.get("keywords", []))
            org["industries"] = ", ".join(org.get("industries", []))

        omit_keys = [
            "industry_tag_id",
            "industry_tag_hash",
            "industry",
            "street_address",
            "snippets_loaded",
            "raw_address",
            "postal_code",
            "organization_revenue",
        ]
        organizations = [{k: v for k, v in o.items() if k not in omit_keys and v is not None} for o in organizations]

        yaml_str = yaml.dump(organizations, indent=2, allow_unicode=True, sort_keys=True)
        return f"{pagination_summary}\n\n```yaml\n{yaml_str}\n```"

    except Exception as e:
        logger.error(f"Error searching organizations: {str(e)}")
        return f"Error: {str(e)}"


async def search_lookalike_companies(
    user_query: str,
    apollo_company_info: dict,
    ideal_customer_profile: str,
    excludes: Optional[List[str]] = [],
    limit: Optional[int] = 10,
) -> dict:
    """
    根据用户查询需求和模板公司详细信息，搜索lookalike公司列表，并返回筛选后的lookalike公司列表

    Args:
        user_query: 用户查询
        apollo_company_info: 模板公司详细信息
        ideal_customer_profile: 理想客户画像
        excludes: 排除的公司列表
        limit: 限制返回公司的数量，默认10

    Returns:
        dict: {"title": str, "lookalike_companies": list[dict]}
    """
    logger.info("Starting search for lookalike companies...")

    llm = init_model(
        # model="gpt-4.1",
        # model="gemini-2.5-flash",
        model="gpt-5-low",
        # max_tokens=20480,
        # temperature=0,
        # include_thoughts=True,
        # thinking_budget=256,
    )
    tools = [search_organizations, tavily_search, bulk_analyze_company_similarity]

    system_prompt = (Path(__file__).parent / "prompts" / "search_lookalike_companies.md").read_text()
    if not system_prompt:
        raise ValueError("Unable to load system prompt file")

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        state_schema=SearchCompaniesState,
        name="search_lookalike_companies_agent",
    )

    reference_company = f"""### Apollo Company Info:
```
{yaml.dump(apollo_company_info, indent=2, allow_unicode=True)}
```

### Ideal Customer Profile:
```
{ideal_customer_profile}
```
"""

    input_message = f"""## 用户查询需求：\n{user_query}

## 模板公司信息：
{reference_company}

## 限制返回的公司数量：{limit}
"""
    if excludes and len(excludes) > 0:
        input_message += f"\n\n## 需要排除的公司列表：\n{json.dumps(excludes)}"

    result = await agent.ainvoke(
        input={
            "messages": [("human", input_message)],
            "user_query": user_query,
            "reference_company": reference_company,
            "excludes": excludes,
            "limit": limit,
            "lookalike_companies": [],
            "lookalike_company_ids": [],
        },
        config=RunnableConfig(recursion_limit=100),
    )

    lookalike_companies = result.get("lookalike_companies", [])
    logger.info(f"Lookalike companies search completed, found {len(lookalike_companies)} lookalike companies")

    return {"title": user_query, "lookalike_companies": lookalike_companies}
