from typing import Any, Dict, Literal, Union

from langchain_core.tools import BaseTool
from pydantic import BaseModel


class FinalAnswerTool(BaseTool):
    name: str = "final_answer"
    description: str = "The final answer to the user's question, use this tool to answer the question"
    return_direct: bool = True
    _explanation: bool = False
    response_format: Literal["content", "content_and_artifact"] = "content_and_artifact"

    def _run(self, **kwargs) -> tuple[dict, Union[dict, BaseModel]]:
        # Check if args_schema is a BaseModel subclass
        if isinstance(self.args_schema, type) and issubclass(self.args_schema, BaseModel):
            # Convert dict to model instance for the second element of tuple
            instance = self.args_schema.model_validate(kwargs)
            return (kwargs, instance)
        return (kwargs, kwargs)

    async def _arun(self, **kwargs) -> tuple[dict, Union[dict, BaseModel]]:
        # Check if args_schema is a BaseModel subclass
        if isinstance(self.args_schema, type) and issubclass(self.args_schema, BaseModel):
            # Convert dict to model instance for the second element of tuple
            instance = self.args_schema.model_validate(kwargs)
            return (kwargs, instance)
        return (kwargs, kwargs)


def create_final_answer_tool(
    schema: Union[BaseModel, Dict[str, Any]],
) -> BaseTool:
    """
    Create a langgraph Tool that accepts structured input and returns it directly.

    This function creates a tool with return_direct=True, meaning the tool's output
    will be returned directly to the user without further processing by the agent.

    Args:
        schema: Either a Pydantic BaseModel class or a JSON schema dictionary

    Returns:
        BaseTool: A langgraph-compatible tool with return_direct=True

    Examples:
        # Using with a Pydantic BaseModel
        class MyOutput(BaseModel):
            result: str
            confidence: float

        tool = create_final_answer_tool(MyOutput)

        # Using with a JSON schema dict
        schema_dict = {
            "type": "object",
            "properties": {
                "result": {"type": "string"},
                "confidence": {"type": "number"}
            },
            "required": ["result", "confidence"]
        }
        tool = create_final_answer_tool(schema_dict)
    """

    # create the final answer tool with return_direct=True
    return FinalAnswerTool(
        args_schema=schema,
    )
