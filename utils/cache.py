"""Custom cache tool that extends diskcache.Cache with enhanced memoize functionality.

Features:
1. Uses parent class memoize by default
2. Disables cache when APP_ENV=development
3. Supports custom key generation function via key parameter
"""

import contextvars
import functools
import inspect
import os
from contextlib import contextmanager
from typing import Any, Callable, Optional

from diskcache import <PERSON><PERSON> as DiskCache
from diskcache.core import EN<PERSON><PERSON><PERSON>, args_to_key, full_name
from langchain_core.utils.env import env_var_is_set

# global context variable to control cache skipping
_skip_cache_context: contextvars.ContextVar[bool] = contextvars.ContextVar("skip_cache", default=False)


@contextmanager
def skip_cache():
    """Context manager to temporarily skip cache for all memoized functions.

    Usage:
        with skip_cache():
            result = some_cached_function()  # this call will skip cache
    """
    token = _skip_cache_context.set(True)
    try:
        yield
    finally:
        _skip_cache_context.reset(token)


def _is_instance_method(func):
    try:
        sig = inspect.signature(func)
        params = list(sig.parameters.keys())
        return len(params) > 0 and params[0] == "self"
    except (ValueError, TypeError):
        return False


class Cache(DiskCache):
    """Enhanced Cache that extends diskcache.Cache with custom memoize functionality."""

    def __init__(self, *args, cache_enabled_env_var: str = "CACHE_ENABLED", default_enabled: bool = True, **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_enabled_env_var = cache_enabled_env_var
        self.default_enabled = default_enabled

    def _cache_enabled(
        self, cache_enabled_env_var: str | None = None, default_enabled: bool | None = None, skip_cache: bool = False
    ) -> bool:
        """Check if cache is enabled.

        Returns:
            bool: True if cache is enabled, False otherwise.
        """
        if default_enabled is None:
            default_enabled = self.default_enabled
        if cache_enabled_env_var is None:
            cache_enabled_env_var = self.cache_enabled_env_var

        return (
            not _skip_cache_context.get()
            and not skip_cache
            and (env_var_is_set(cache_enabled_env_var) if cache_enabled_env_var in os.environ else default_enabled)
        )

    def memoize(
        self,
        name: Optional[str] = None,
        typed: bool = False,
        expire: Optional[float] = None,
        tag: Optional[str] = None,
        ignore: set[str] = (),
        key: Optional[Callable] = None,
        default_enabled: bool | None = None,
        cache_enabled_env_var: str | None = None,
    ):
        """Enhanced memoizing cache decorator.

        Extends parent's memoize with:
        - Development environment bypass (APP_ENV=development)
        - Custom key generation function support
        - Context-based cache skipping via skip_cache() context manager

        Args:
            name: name given for callable (default None, automatic)
            typed: cache different types separately (default False)
            expire: seconds until arguments expire (default None, no expiry)
            tag: text to associate with arguments (default None)
            ignore: positional or keyword args to ignore (default ())
            key: custom function to generate cache key from args and kwargs
            default_enabled: default cache enabled, if not provided, use Cache's default_enabled
            cache_enabled_env_var: cache enabled environment variable, if not provided, use Cache's cache_enabled_env_var
        Returns:
            callable decorator
        """  # noqa: E501

        super_memoize = super().memoize(name, typed, expire, tag, ignore)

        def decorator(func):
            super_wrapper = super_memoize(func)
            is_instance_method = _is_instance_method(func)
            base = (full_name(func),) if name is None else (name,)

            def __cache_key__(*args, **kwargs):
                """Make key for cache given function arguments."""
                _args = args[1:] if is_instance_method else args
                if key:
                    return key(*_args, **kwargs)
                return args_to_key(base, _args, kwargs, typed, ignore)

            super_wrapper.__cache_key__ = __cache_key__

            if inspect.iscoroutinefunction(func):

                @functools.wraps(func)
                async def wrapper(*args, _skip_cache: bool = False, **kwargs) -> Any:
                    """Async wrapper for callable to cache arguments and return values."""
                    if not self._cache_enabled(cache_enabled_env_var, default_enabled, _skip_cache):
                        return await func(*args, **kwargs)
                    cache_key = wrapper.__cache_key__(*args, **kwargs)
                    result = self.get(cache_key, default=ENOVAL, retry=True)

                    if result is ENOVAL:
                        result = await func(*args, **kwargs)
                        # only set cache if expire is None or > 0 (following diskcache pattern)
                        if expire is None or expire > 0:
                            self.set(cache_key, result, expire, tag=tag, retry=True)

                    return result

            else:

                @functools.wraps(func)
                def wrapper(*args, _skip_cache: bool = False, **kwargs) -> Any:
                    """Sync wrapper for callable to cache arguments and return values."""
                    if not self._cache_enabled(cache_enabled_env_var, default_enabled, _skip_cache):
                        return func(*args, **kwargs)

                    return super_wrapper(*args, **kwargs)

            wrapper.__cache_key__ = __cache_key__
            wrapper.skip_cache = func
            return wrapper

        return decorator
