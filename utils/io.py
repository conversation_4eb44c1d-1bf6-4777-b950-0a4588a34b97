import inspect
from pathlib import Path


def read_relative_file(filename: str) -> str:
    """
    Read the content of a file relative to the caller's file.

    Args:
        filename: The name of the file to read.

    Returns:
        The content of the file.
    """
    caller_frame = inspect.stack()[1]
    caller_path = Path(caller_frame.filename).resolve()
    return (caller_path / filename).read_text(encoding="utf-8").strip()
