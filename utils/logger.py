import contextvars
import logging
import sys
import time
from contextlib import asynccontextmanager
from typing import Literal

import colorlog
from loguru import logger

from config import LOG_LEVEL

# 使用 contextvars 存储 sid
sid = contextvars.ContextVar("sid", default="none")

# 日志级别配置
# DEFAULT_LOG_LEVEL 从 config 导入
LOG_LEVEL_ENV_VAR = "LOG_LEVEL"  # 环境变量名称


def get_log_level() -> int:
    """从服务器环境变量获取日志级别

    优先级：
    1. 服务器环境变量 LOG_LEVEL
    2. 如果未设置，使用默认值 INFO
    """
    # 从服务器环境变量中获取日志级别
    log_level_str = LOG_LEVEL.upper()

    # 将字符串日志级别转换为 logging 模块的常量
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    # 如果日志级别无效，使用默认的 INFO 级别
    log_level = log_levels.get(log_level_str)
    if log_level is None:
        print(f"\u8b66告: 无效的日志级别 '{log_level_str}'，使用默认级别 'INFO'")
        log_level = logging.INFO

    return log_level


class SessionAwareColoredFormatter(colorlog.ColoredFormatter):
    """结合会话ID和彩色输出的日志格式化器"""

    _base_fmt = "%(log_color)s%(asctime)s.%(msecs)03d %(levelname)-7s%(reset)s %(name)s - %(message)s"
    _session_fmt = "%(log_color)s%(asctime)s.%(msecs)03d %(levelname)-7s%(reset)s [%(blue)s%(session_id)s%(reset)s] %(name)s - %(message)s"  # noqa: E501

    def __init__(
        self,
        fmt=None,
        datefmt=None,
        style: Literal["%", "{", "$"] = "%",
        log_colors=None,
        reset=True,
        secondary_log_colors=None,
    ):
        super().__init__(self._base_fmt, datefmt, style, log_colors, reset, secondary_log_colors)
        self.converter = time.localtime  # 使用本地时区而不是UTC

    def format(self, record):
        # 从 contextvars 中获取 session_id
        if not hasattr(record, "session_id"):
            record.session_id = sid.get()

        # 根据 session_id 是否有效来动态设置格式
        session_id = record.session_id
        if session_id and session_id != "none":
            # 有有效的 session_id，包含在格式中
            self._style._fmt = self._session_fmt
        else:
            # 没有有效的 session_id，不显示括号部分
            self._style._fmt = self._base_fmt

        return super().format(record)


def configure_loguru():
    # remove the default loguru formatter
    logger.remove()

    def loguru_formatter(record):
        """
        loguru formatter, add task_id before message, if task_id is in extra
        """
        message = "<level>{message}</level>"
        if record["extra"].get("task_id"):
            message = "<magenta>{extra[task_id]}</magenta> " + message
        if record["extra"].get("sid"):
            message = "<magenta>{extra[sid]}</magenta> " + message

        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> "
            "<level>{level: <8}</level>"
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - " + message + "\n{exception}"
        )

    logger.add(
        sys.stderr,
        format=loguru_formatter,
    )


def configure_logging():
    """配置全局日志设置"""
    # 创建根日志记录器
    root_logger = logging.getLogger()
    # 设置根日志记录器级别为最低级别，以便处理器可以根据各自的级别过滤日志
    root_logger.setLevel(logging.DEBUG)

    # 清除已存在的处理器
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    # 使用彩色日志格式化器
    console_formatter = SessionAwareColoredFormatter(
        datefmt="%Y-%m-%d %H:%M:%S",  # 设置日期时间格式
        log_colors={
            "DEBUG": "cyan",
            "INFO": "green",
            "WARNING": "yellow",
            "ERROR": "red",
            "CRITICAL": "red,bg_white",
        },
        secondary_log_colors={},
        reset=True,
        style="%",
    )

    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(get_log_level())  # 从环境变量读取日志级别
    root_logger.addHandler(console_handler)

    # 设置第三方库的日志级别
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("playwright").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARN)
    logging.getLogger("openai").setLevel(logging.WARN)
    logging.getLogger("langchain").setLevel(logging.INFO)
    logging.getLogger("langfuse").setLevel(logging.INFO)
    logging.getLogger("pymongo").setLevel(logging.INFO)
    logging.getLogger("langchain_aws").setLevel(logging.WARNING)

    return root_logger


@asynccontextmanager
async def session_id_context(session_id: str):
    token = sid.set(session_id)
    try:
        yield
    finally:
        sid.reset(token)


# 获取一个带有会话ID上下文的日志记录器
def get_logger(name: str) -> logging.Logger:
    """获取一个配置好的日志记录器"""
    logger = logging.getLogger(name)
    return logger


# 初始化时配置全局日志
configure_logging()
