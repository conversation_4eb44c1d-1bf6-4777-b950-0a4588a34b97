from typing import Any, Dict, Optional, Type

from langchain_core.messages import AIMessageChunk
from langchain_core.outputs import ChatGenerationChunk
from langchain_xai import ChatXAI


class CustomChatXAI(ChatXAI):
    """Custom ChatXAI implementation with enhanced reasoning content handling."""

    def _convert_chunk_to_generation_chunk(
        self,
        chunk: Dict[str, Any],
        default_chunk_class: Type[ChatGenerationChunk],
        base_generation_info: Optional[Dict[str, Any]],
    ) -> Optional[ChatGenerationChunk]:
        generation_chunk = super()._convert_chunk_to_generation_chunk(
            chunk,
            default_chunk_class,
            base_generation_info,
        )

        if (choices := chunk.get("choices")) and generation_chunk:
            top = choices[0]
            if isinstance(generation_chunk.message, AIMessageChunk):
                if reasoning := top.get("delta", {}).get("reasoning"):
                    generation_chunk.message.additional_kwargs["reasoning"] = reasoning

        return generation_chunk
